/* eslint-disable no-unused-vars */
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

export interface IEmailService {
  /**
   * Send an email
   */
  sendEmail(options: EmailOptions): Promise<void>;

  /**
   * Send a welcome email to a new user
   */
  sendWelcomeEmail(email: string, name: string, userType: string): Promise<void>;

  /**
   * Send a verification email
   */
  sendVerificationEmail(
    email: string,
    name: string,
    verificationLink: string,
  ): Promise<void>;

  /**
   * Send a password reset email
   */
  sendPasswordResetEmail(email: string, name: string, resetLink: string): Promise<void>;

  /**
   * Send a password changed confirmation email
   */
  sendPasswordChangedEmail(email: string, name: string): Promise<void>;

  /**
   * Send a shipment created notification
   */
  sendShipmentCreatedEmail(
    email: string,
    name: string,
    shipmentId: string,
    pickupCode: string,
  ): Promise<void>;

  /**
   * Send a shipment status update notification
   */
  sendShipmentStatusUpdateEmail(
    email: string,
    name: string,
    shipmentId: string,
    status: string,
    details?: string,
  ): Promise<void>;

  /**
   * Send a notification to Access Operator about new shipment
   */
  sendNewShipmentNotificationToAO(
    email: string,
    businessName: string,
    shipmentId: string,
  ): Promise<void>;

  /**
   * Send a notification to Car Operator about available shipment
   */
  sendAvailableShipmentToCO(
    email: string,
    name: string,
    shipmentId: string,
    originLocation: string,
    destLocation: string,
  ): Promise<void>;

  /**
   * Send a welcome email to a new admin
   */
  sendAdminWelcomeEmail(email: string, name: string, role: string): Promise<void>;

  /**
   * Send an admin password reset email
   */
  sendAdminPasswordResetEmail(email: string, name: string, resetLink: string): Promise<void>;

  /**
   * Send an admin password changed confirmation email
   */
  sendAdminPasswordChangedEmail(email: string, name: string): Promise<void>;

  /**
   * Send an OTP verification email to admin
   */
  sendAdminOTPEmail(email: string, name: string, otp: string): Promise<void>;

  /**
   * Send modern shipment created notification
   */
  sendModernShipmentCreatedEmail(
    email: string,
    name: string,
    shipmentId: string,
    pickupCode: string,
    trackingCode: string,
  ): Promise<void>;

  /**
   * Send modern shipment status update notification
   */
  sendModernShipmentStatusUpdateEmail(
    email: string,
    name: string,
    shipmentId: string,
    status: string,
    trackingCode: string,
    details?: string,
  ): Promise<void>;
}
