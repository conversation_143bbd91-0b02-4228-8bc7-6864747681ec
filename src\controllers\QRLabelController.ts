import { <PERSON>uff<PERSON> } from 'buffer';

import { Request, Response, NextFunction } from 'express';
import PDFDocument from 'pdfkit';
import QRCode from 'qrcode';
import { z } from 'zod';

import { UUIDSchema } from '../schemas';
import { ServiceFactory } from '../services/ServiceFactory';
import { UserType } from '../types/models';
import { AppError } from '../utils/errors';

// Schema for generating QR for shipment - geo coordinates will be auto-extracted from origin AO
const GenerateQRForShipmentSchema = z.object({
  shipment_id: UUIDSchema,
  photo_url: z.string().url().optional(),
  notes: z.string().optional(),
  // geo_latitude and geo_longitude removed - will be auto-extracted from origin AO
});

// Schema for generating QR labels (without shipment)
const GenerateQRLabelsSchema = z.object({
  count: z.number().min(1).max(50), // Limit to reasonable number
});

// Schema for using unused QR for shipment
const UseUnusedQRSchema = z.object({
  shipment_id: UUIDSchema,
});

export class QRLabelController {
  /**
   * Generate QR label for specific shipment
   * POST /qr-labels/generate-for-shipment
   */
  static async generateQRForShipment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      if (!userId) {
        throw AppError.unauthorized('Authentication required');
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        throw AppError.forbidden('Only Access Operators can generate QR labels');
      }

      // Validate request body
      const validatedData = GenerateQRForShipmentSchema.parse(req.body);
      const { shipment_id, photo_url, notes } = validatedData;

      // Get QR Label Service
      const qrLabelService = ServiceFactory.getQRLabelService();

      // Generate QR label for the shipment (geo coordinates will be auto-extracted from origin AO)
      const qrLabel = await qrLabelService.generateQRLabelForShipment(
        userId,
        shipment_id,
        photo_url,
        notes,
        userId
      );

      res.status(201).json({
        success: true,
        message: 'QR label generated successfully for shipment',
        data: {
          qr_label: {
            id: qrLabel.id,
            qr_value: qrLabel.qr_value,
            status: qrLabel.status,
            shipment_id: qrLabel.shipment_id,
            assigned_at: qrLabel.assigned_at,
          }
        }
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Generate QR labels for AO (without specific shipment)
   * POST /qr-labels/generate
   */
  static async generateQRLabels(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      if (!userId) {
        throw AppError.unauthorized('Authentication required');
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        throw AppError.forbidden('Only Access Operators can generate QR labels');
      }

      // Validate request body
      const validatedData = GenerateQRLabelsSchema.parse(req.body);
      const { count } = validatedData;

      // Get QR Label Service
      const qrLabelService = ServiceFactory.getQRLabelService();

      // Generate QR labels
      const qrLabels = await qrLabelService.generateQRLabels(userId, count, userId);

      res.status(201).json({
        success: true,
        message: `${count} QR labels generated successfully`,
        data: {
          qr_labels: qrLabels.map(label => ({
            id: label.id,
            qr_value: label.qr_value,
            status: label.status,
            created_at: label.created_at,
          })),
          count: qrLabels.length,
        }
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Use any unused QR label for a shipment
   * POST /qr-labels/use-for-shipment
   */
  static async useUnusedQRForShipment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      if (!userId) {
        throw AppError.unauthorized('Authentication required');
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        throw AppError.forbidden('Only Access Operators can use QR labels');
      }

      // Validate request body
      const validatedData = UseUnusedQRSchema.parse(req.body);
      const { shipment_id } = validatedData;

      // Get QR Label Service
      const qrLabelService = ServiceFactory.getQRLabelService();

      // Use unused QR label for the shipment
      const qrLabel = await qrLabelService.useUnusedQRLabelForShipment(userId, shipment_id, userId);

      res.status(200).json({
        success: true,
        message: 'QR label assigned to shipment successfully',
        data: {
          qr_label: {
            id: qrLabel.id,
            qr_value: qrLabel.qr_value,
            status: qrLabel.status,
            shipment_id: qrLabel.shipment_id,
            assigned_at: qrLabel.assigned_at,
          }
        }
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Get unused QR labels for AO
   * GET /qr-labels/unused
   */
  static async getUnusedQRLabels(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      if (!userId) throw AppError.unauthorized('Authentication required');
      if (userType !== UserType.ACCESS_OPERATOR) throw AppError.forbidden('Only Access Operators can view QR labels');

      // Pagination params
      const page = parseInt((req.query.page as string) ?? '0', 10);
      const limit = Math.min(parseInt((req.query.limit as string) ?? '10', 10), 100);
      const skip = page * limit;

      const qrLabelService = ServiceFactory.getQRLabelService();
      const { qrLabels, total } = await qrLabelService.getUnusedQRLabels(userId, { skip, take: limit });

      res.status(200).json({
        success: true,
        message: 'Unused QR labels retrieved successfully',
        data: {
          qr_labels: qrLabels.map(l => ({
            id: l.id,
            qr_value: l.qr_value,
            status: l.status,
            created_at: l.created_at,
          })),
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: skip + limit < total,
            hasPrev: page > 0,
          },
        },
      });
    } catch (err) {
      next(err);
    }
  }

  /**
   * Get unused QR labels for AO
   * GET /qr-labels/unused
   */
  static async generateQRLabelsPDF(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      /* ------------------------------------------------------------------
       * 0.  Auth & validation
       * ----------------------------------------------------------------*/
      const userId = req.user?.id;
      if (!userId) throw AppError.unauthorized('Authentication required');
      if (req.user?.user_type !== UserType.ACCESS_OPERATOR)
        throw AppError.forbidden('Only Access Operators can generate QR labels');
  
      const { count } = GenerateQRLabelsSchema.parse(req.body);
      const qrLabels = await ServiceFactory.getQRLabelService().generateQRLabels(
        userId,
        count,
        userId
      );
  
      /* ------------------------------------------------------------------
       * 1.  Access‑operator meta
       * ----------------------------------------------------------------*/
      const { email: aoEmail = '---', name: aoName = 'Access Operator' } =
        (await ServiceFactory.getPrisma().user.findUnique({
          where: { id: userId },
          select: { email: true, name: true },
        })) || {};
  
      /* ------------------------------------------------------------------
       * 2.  PDF setup
       *      – We lower the bottom margin to avoid the automatic page‑break
       *        that PDFKit triggers when text is written below that margin.
       * ----------------------------------------------------------------*/
      const margin = 45; // left / right / top margin for main content
      const bottomMargin = 10; // small, footer sits outside the main margin band
  
      const doc = new PDFDocument({
        autoFirstPage: false,
        size: 'A4',
        margins: { top: margin, left: margin, right: margin, bottom: bottomMargin },
      });
  
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=qr_labels_${Date.now()}.pdf`
      );
      doc.pipe(res);
  
      /* ------------------------------------------------------------------
       * 3.  Style constants & geometry numbers
       * ----------------------------------------------------------------*/
      const colors = {
        primary: '#2563EB',
        accent: '#3B82F6',
        text: '#1F2937',
        textLight: '#6B7280',
        border: '#E5E7EB',
        background: '#F8FAFC',
        white: '#FFFFFF',
      } as const;
  
      const headerH = 85;
      const footerH = 45;
      const qrSize = 120;
      const gap = 18;
      const labelH = qrSize + 30; // QR + caption area
  
      /* ------------------------------------------------------------------
       * 4.  State vars (re‑calculated on every new page)
       * ----------------------------------------------------------------*/
      let maxCols = 0;
      let maxRows = 0;
      let itemsPerPage = 0;
  
      let curX = 0;
      let curY = 0;
      let pageNo = 0;
  
      /* ------------------------------------------------------------------
       * 5.  Helper: new page with header / footer ("chrome")
       * ----------------------------------------------------------------*/
      const addPageWithChrome = () => {
        doc.addPage();
        pageNo += 1;
  
        /** --------------------------- HEADER --------------------------- */
        doc.rect(0, 0, doc.page.width, headerH).fill(colors.background);
        doc
          .fillColor(colors.primary)
          .font('Helvetica-Bold')
          .fontSize(22)
          .text('QR Access Labels', margin, 22);
  
        doc
          .fillColor(colors.text)
          .font('Helvetica')
          .fontSize(12)
          .text(`Generated for ${aoName}`, margin, 50);
  
        doc.fillColor(colors.textLight).fontSize(10).text(aoEmail, margin, 65);
  
        /** --------------------------- FOOTER --------------------------- */
        const footerY = doc.page.height - footerH; // very bottom band
        doc.rect(0, footerY, doc.page.width, footerH).fill(colors.background);
  
        // Write the page number **within** the footer band, centred.
        doc
          .fillColor(colors.textLight)
          .font('Helvetica')
          .fontSize(10)
          .text(`Page ${pageNo}`, 0, footerY + 18, {
            align: 'center',
          });
  
        // After writing in the footer, reset the internal pointer so that
        // subsequent text operations (captions) do NOT inherit the near‑bottom
        // y‑coordinate and accidentally trigger an implicit page‑break.
        doc.y = headerH + margin;
  
        /** --------------------------- GRID CALC ------------------------ */
        maxCols = Math.max(
          1,
          Math.floor((doc.page.width - margin * 2 + gap) / (qrSize + gap))
        );
  
        const availableH =
          doc.page.height - headerH - footerH - margin - bottomMargin;
        maxRows = Math.max(1, Math.floor((availableH + gap) / (labelH + gap)));
  
        itemsPerPage = maxCols * maxRows;
  
        curX = margin;
        curY = headerH + margin;
      };
  
      /* ------------------------------------------------------------------
       * 6.  First page & QR grid rendering
       * ----------------------------------------------------------------*/
      addPageWithChrome();
  
      for (let i = 0; i < qrLabels.length; i++) {
        if (i > 0 && i % itemsPerPage === 0) addPageWithChrome();
  
        const label = qrLabels[i];
  
        // High‑resolution QR bitmap (dark on white)
        const pngBase64 = await QRCode.toDataURL(label.qr_value, {
          errorCorrectionLevel: 'H',
          margin: 1,
          width: qrSize * 2,
          color: { dark: colors.text, light: colors.white },
        });
        const qrBuffer = Buffer.from(pngBase64.split(',')[1], 'base64');
  
        /** --------------------------- CARD ---------------------------- */
        doc
          .save()
          .rect(curX - 4, curY - 4, qrSize + 8, labelH)
          .fill(colors.white)
          .stroke(colors.border)
          .restore();
  
        // QR image
        doc.image(qrBuffer, curX, curY, { width: qrSize, height: qrSize });
  
        // Caption value
        doc
          .fillColor(colors.primary)
          .font('Helvetica-Bold')
          .fontSize(10)
          .text(label.qr_value, curX, curY + qrSize + 4, {
            width: qrSize,
            align: 'center',
          });
  
        // Caption prompt
        doc
          .fillColor(colors.textLight)
          .font('Helvetica')
          .fontSize(8)
          .text('SCAN ME', curX, curY + qrSize + 18, {
            width: qrSize,
            align: 'center',
          });
  
        /** --------------------------- CURSOR ADVANCE ------------------ */
        if ((i + 1) % maxCols === 0) {
          // Next row
          curX = margin;
          curY += labelH + gap;
        } else {
          // Next column
          curX += qrSize + gap;
        }
      }
  
      /* ------------------------------------------------------------------
       * 7.  Finalise stream
       * ----------------------------------------------------------------*/
      doc.end();
    } catch (err) {
      if (err instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: err.errors.map((e) => ({ field: e.path.join('.'), message: e.message })),
        });
      }
      next(err);
    }
  }
}