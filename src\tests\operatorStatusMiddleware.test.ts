/* eslint-env jest */
import { Request, Response, NextFunction } from 'express';
import { requireActiveAndApprovedOperator } from '../middleware/operatorStatusMiddleware';
import { UserType, UserStatus } from '../types/models';

// Declare Jest globals for TypeScript
declare const jest: any,
  describe: any,
  it: any,
  expect: any,
  beforeEach: any,
  afterEach: any;

// Mock ServiceFactory
jest.mock('../services/ServiceFactory', () => ({
  ServiceFactory: {
    getPrismaClient: jest.fn(),
  },
}));

import { ServiceFactory } from '../services/ServiceFactory';

// Create mock Prisma client
const createMockPrisma = () => {
  return {
    user: {
      findUnique: jest.fn(),
    },
  } as any;
};

describe('requireActiveAndApprovedOperator middleware', () => {
  let mockPrisma: ReturnType<typeof createMockPrisma>;
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    mockPrisma = createMockPrisma();
    (ServiceFactory.getPrismaClient as jest.Mock).mockReturnValue(mockPrisma);

    req = {
      user: undefined,
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return 401 if user is not authenticated', async () => {
    req.user = undefined;

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Authentication required',
      error: {
        type: 'AUTHENTICATION_ERROR',
        details: 'User not authenticated',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should return 401 if user token is missing required information', async () => {
    req.user = { id: 'user-id' }; // Missing user_type

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Invalid authentication token',
      error: {
        type: 'AUTHENTICATION_ERROR',
        details: 'Token missing required user information',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should return 404 if user is not found in database', async () => {
    req.user = { id: 'user-id', user_type: UserType.CUSTOMER };
    mockPrisma.user.findUnique.mockResolvedValue(null);

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'User not found',
      error: {
        type: 'NOT_FOUND_ERROR',
        details: 'User account could not be found',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should return 403 if user is not active', async () => {
    req.user = { id: 'user-id', user_type: UserType.CUSTOMER };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.PENDING,
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Account is not active',
      error: {
        type: 'AUTHORIZATION_ERROR',
        details: 'Your account is pending approval, suspended, or inactive. Please contact support.',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should allow active customer users', async () => {
    req.user = { id: 'user-id', user_type: UserType.CUSTOMER };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  it('should return 403 if access operator is not approved', async () => {
    req.user = { id: 'user-id', user_type: UserType.ACCESS_OPERATOR };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
      accessOperator: {
        approved: false,
      },
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Access Operator account pending approval',
      error: {
        type: 'AUTHORIZATION_ERROR',
        details: 'Your Access Operator account is pending admin approval. You can update your profile but cannot perform operational tasks until approved.',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should allow active and approved access operators', async () => {
    req.user = { id: 'user-id', user_type: UserType.ACCESS_OPERATOR };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
      accessOperator: {
        approved: true,
      },
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  it('should return 403 if car operator is not approved', async () => {
    req.user = { id: 'user-id', user_type: UserType.CAR_OPERATOR };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
      carOperator: {
        approved: false,
      },
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Car Operator account pending approval',
      error: {
        type: 'AUTHORIZATION_ERROR',
        details: 'Your Car Operator account is pending admin approval. You can update your profile but cannot perform operational tasks until approved.',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should allow active and approved car operators', async () => {
    req.user = { id: 'user-id', user_type: UserType.CAR_OPERATOR };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
      carOperator: {
        approved: true,
      },
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  it('should allow active admin users', async () => {
    req.user = { id: 'user-id', user_type: UserType.ADMIN };
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-id',
      status: UserStatus.ACTIVE,
    });

    await requireActiveAndApprovedOperator(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });
});
