/* eslint-disable no-unused-vars */
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcrypt";

import { UserStatus, UserType } from "../types/models";
import { AppError } from "../utils/errors";

export class AccessOperatorService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Create a new access operator (Access Operator only)
   */
  async createAccessOperator(data: {
    name: string;
    email: string;
    password: string;
    phone: string;
    business_name: string;
    address: string;
    geo_latitude: number;
    geo_longitude: number;
    approved?: boolean;
  }): Promise<any> {
    try {
      // Check if email already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: data.email },
      });

      if (existingUser) {
        throw AppError.badRequest("Email already exists");
      }

      // Validate geo coordinates
      if (data.geo_latitude < -90 || data.geo_latitude > 90) {
        throw AppError.badRequest("Latitude must be between -90 and 90");
      }

      if (data.geo_longitude < -180 || data.geo_longitude > 180) {
        throw AppError.badRequest("Longitude must be between -180 and 180");
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // Create user and access operator in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            name: data.name,
            email: data.email,
            password_hash: hashedPassword,
            phone: data.phone,
            user_type: UserType.ACCESS_OPERATOR,
            status: UserStatus.ACTIVE,
            email_verified: true, // Auto-verified for access operator created accounts
          },
        });

        // Create access operator
        const accessOperator = await tx.accessOperator.create({
          data: {
            id: user.id,
            business_name: data.business_name,
            address: data.address,
            geo_latitude: data.geo_latitude,
            geo_longitude: data.geo_longitude,
            approved: data.approved !== undefined ? data.approved : false, // Default to pending approval
          },
        });

        return { user, accessOperator };
      });

      // Return the created access operator with user data
      return {
        id: result.user.id,
        name: result.user.name,
        email: result.user.email,
        phone: result.user.phone,
        user_type: result.user.user_type,
        status: result.user.status,
        email_verified: result.user.email_verified,
        business_name: result.accessOperator.business_name,
        address: result.accessOperator.address,
        geo_latitude: result.accessOperator.geo_latitude,
        geo_longitude: result.accessOperator.geo_longitude,
        approved: result.accessOperator.approved,
        created_at: result.user.created_at,
        updated_at: result.user.updated_at,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error creating access operator:", error);
      throw AppError.internal("Failed to create access operator");
    }
  }

  /**
   * Get all access operators with pagination, filtering, and searching
   * Only returns active and approved access operators
   */
  async getAllAccessOperators(options?: {
    skip?: number;
    take?: number;
    search?: string;
    approved?: boolean;
    status?: string;
  }): Promise<{ accessOperators: any[]; total: number }> {
    try {
      const { skip = 0, take = 10, search, approved, status } = options || {};

      // Build where conditions - be more flexible with defaults
      const where: any = {
        user: {
          user_type: UserType.ACCESS_OPERATOR,
        },
      };

      // Only filter by status if explicitly provided
      if (status) {
        where.user.status = status;
      }

      // Only filter by approval if explicitly provided
      if (approved !== undefined) {
        where.approved = approved;
      }

      console.info(
        "AccessOperatorService.getAllAccessOperators - Query conditions:",
        JSON.stringify(where, null, 2)
      );
      console.info("AccessOperatorService.getAllAccessOperators - Options:", {
        skip,
        take,
        search,
        approved,
        status,
      });

      // Add search condition if provided
      if (search) {
        where.OR = [
          { business_name: { contains: search, mode: "insensitive" } },
          { address: { contains: search, mode: "insensitive" } },
          { user: { name: { contains: search, mode: "insensitive" } } },
          { user: { email: { contains: search, mode: "insensitive" } } },
        ];
      }

      // Get total count for pagination
      const total = await this.prisma.accessOperator.count({ where });
      console.info(
        `AccessOperatorService.getAllAccessOperators - Total count: ${total}`
      );

      // Also get total count without any filters for debugging
      const totalAll = await this.prisma.accessOperator.count();
      console.info(
        `AccessOperatorService.getAllAccessOperators - Total count (all): ${totalAll}`
      );

      // Get access operators with pagination
      const accessOperators = await this.prisma.accessOperator.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              status: true,
              email_verified: true,
              created_at: true,
              updated_at: true,
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
        skip,
        take,
      });

      // Transform the data to include user fields at the top level
      const transformedAccessOperators = accessOperators.map((ao) => ({
        id: ao.id,
        name: ao.user.name,
        email: ao.user.email,
        phone: ao.user.phone,
        status: ao.user.status,
        email_verified: ao.user.email_verified,
        business_name: ao.business_name,
        address: ao.address,
        geo_latitude: ao.geo_latitude,
        geo_longitude: ao.geo_longitude,
        approved: ao.approved,
        created_at: ao.created_at,
        updated_at: ao.user.updated_at,
      }));

      return { accessOperators: transformedAccessOperators, total };
    } catch (error) {
      console.error("Error getting all access operators:", error);
      throw AppError.internal("Failed to get access operators");
    }
  }

  /**
   * Get access operator by ID
   * Relaxed filtering for development (removed approval/status requirements)
   */
  async getAccessOperatorById(id: string): Promise<any | null> {
    try {
      console.info(
        `AccessOperatorService.getAccessOperatorById called with id: ${id}`
      );

      // First check if any access operator exists with this ID (for debugging)
      const anyAccessOperator = await this.prisma.accessOperator.findUnique({
        where: { id },
        select: {
          id: true,
          approved: true,
          user: { select: { status: true } },
        },
      });
      console.info(
        `AccessOperatorService.getAccessOperatorById - Raw check result:`,
        anyAccessOperator
      );

      const accessOperator = await this.prisma.accessOperator.findFirst({
        where: {
          id,
          // Removed strict approval and status checks for development
          // approved: true,
          // user: {
          //   status: UserStatus.ACTIVE,
          // },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              status: true,
              email_verified: true,
              created_at: true,
              updated_at: true,
            },
          },
        },
      });

      if (!accessOperator) {
        console.info(
          `AccessOperatorService.getAccessOperatorById - No access operator found with id: ${id}`
        );
        return null;
      }

      console.info(
        `AccessOperatorService.getAccessOperatorById - Found access operator: ${accessOperator.id}`
      );

      // Transform the data to include user fields at the top level
      return {
        id: accessOperator.id,
        name: accessOperator.user.name,
        email: accessOperator.user.email,
        phone: accessOperator.user.phone,
        status: accessOperator.user.status,
        email_verified: accessOperator.user.email_verified,
        business_name: accessOperator.business_name,
        address: accessOperator.address,
        geo_latitude: accessOperator.geo_latitude,
        geo_longitude: accessOperator.geo_longitude,
        approved: accessOperator.approved,
        created_at: accessOperator.created_at,
        updated_at: accessOperator.user.updated_at,
      };
    } catch (error) {
      console.error("Error getting access operator by ID:", error);
      throw AppError.internal("Failed to get access operator");
    }
  }

  /**
   * Update access operator
   */
  async updateAccessOperator(
    id: string,
    data: {
      name?: string;
      phone?: string;
      business_name?: string;
      address?: string;
      geo_latitude?: number;
      geo_longitude?: number;
    }
  ): Promise<any> {
    try {
      // Check if access operator exists
      const existingOperator = await this.prisma.accessOperator.findUnique({
        where: { id },
        include: {
          user: true,
        },
      });

      if (!existingOperator) {
        throw AppError.notFound("Access operator not found");
      }

      // Validate geo coordinates if provided
      if (
        data.geo_latitude !== undefined &&
        (data.geo_latitude < -90 || data.geo_latitude > 90)
      ) {
        throw AppError.badRequest("Latitude must be between -90 and 90");
      }

      if (
        data.geo_longitude !== undefined &&
        (data.geo_longitude < -180 || data.geo_longitude > 180)
      ) {
        throw AppError.badRequest("Longitude must be between -180 and 180");
      }

      // Prepare update data for User table
      const userUpdateData: any = {};
      if (data.name) userUpdateData.name = data.name;
      if (data.phone) userUpdateData.phone = data.phone;

      // Prepare update data for AccessOperator table
      const accessOperatorUpdateData: any = {};
      if (data.business_name)
        accessOperatorUpdateData.business_name = data.business_name;
      if (data.address) accessOperatorUpdateData.address = data.address;
      if (data.geo_latitude !== undefined)
        accessOperatorUpdateData.geo_latitude = data.geo_latitude;
      if (data.geo_longitude !== undefined)
        accessOperatorUpdateData.geo_longitude = data.geo_longitude;

      // Update both user and access operator data in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Update user data if there are user fields to update
        if (Object.keys(userUpdateData).length > 0) {
          await tx.user.update({
            where: { id },
            data: userUpdateData,
          });
        }

        // Update access operator data if there are access operator fields to update
        if (Object.keys(accessOperatorUpdateData).length > 0) {
          await tx.accessOperator.update({
            where: { id },
            data: accessOperatorUpdateData,
          });
        }

        // Return updated access operator with user data
        const updatedOperator = await tx.accessOperator.findUnique({
          where: { id },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                status: true,
                email_verified: true,
                created_at: true,
                updated_at: true,
              },
            },
          },
        });

        // Transform the data to include user fields at the top level
        return {
          id: updatedOperator!.id,
          name: updatedOperator!.user.name,
          email: updatedOperator!.user.email,
          phone: updatedOperator!.user.phone,
          status: updatedOperator!.user.status,
          email_verified: updatedOperator!.user.email_verified,
          business_name: updatedOperator!.business_name,
          address: updatedOperator!.address,
          geo_latitude: updatedOperator!.geo_latitude,
          geo_longitude: updatedOperator!.geo_longitude,
          approved: updatedOperator!.approved,
          created_at: updatedOperator!.created_at,
          updated_at: updatedOperator!.user.updated_at,
        };
      });

      return result;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error updating access operator:", error);
      throw AppError.internal("Failed to update access operator");
    }
  }
}
