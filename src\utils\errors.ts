import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

// Custom error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  FORBIDDEN = 'FORBIDDEN_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  CONFLICT = 'CONFLICT_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
  BAD_REQUEST = 'BAD_REQUEST_ERROR',
}

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly details?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.INTERNAL,
    statusCode: number = 500,
    details?: any,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    Error.captureStackTrace(this, this.constructor);
  }

  static validation(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.VALIDATION, 400, details);
  }

  static authentication(message: string = 'Authentication required'): AppError {
    return new AppError(message, ErrorType.AUTHENTICATION, 401);
  }

  static unauthorized(message: string = 'Not authenticated'): AppError {
    return new AppError(message, ErrorType.AUTHENTICATION, 401);
  }

  static authorization(message: string = 'Permission denied'): AppError {
    return new AppError(message, ErrorType.AUTHORIZATION, 403);
  }

  static forbidden(message: string = 'Forbidden access'): AppError {
    return new AppError(message, ErrorType.FORBIDDEN, 403);
  }

  static notFound(message: string = 'Resource not found'): AppError {
    return new AppError(message, ErrorType.NOT_FOUND, 404);
  }

  static conflict(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.CONFLICT, 409, details);
  }

  static badRequest(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.BAD_REQUEST, 400, details);
  }

  static internal(message: string = 'Internal server error'): AppError {
    return new AppError(message, ErrorType.INTERNAL, 500);
  }

  static notImplemented(message: string = 'Feature not implemented'): AppError {
    return new AppError(message, ErrorType.INTERNAL, 501);
  }

  /**
   * Handle error and send appropriate response
   * This method can be used in controller catch blocks to handle errors
   */
  static handleError(err: any, _req: Request, res: Response): void {
    console.error('Error:', err);

    // Handle Zod validation errors
    if (err instanceof ZodError) {
      const formattedErrors = formatZodError(err);
      res.status(400).json({
        success: false,
        error: {
          type: ErrorType.VALIDATION,
          message: 'Validation error',
          details: formattedErrors,
        },
      });
      return;
    }

    // Handle custom AppError
    if (err instanceof AppError) {
      res.status(err.statusCode).json({
        success: false,
        error: {
          type: err.type,
          message: err.message,
          details: err.details,
        },
      });
      return;
    }

    // Handle other errors
    const isDev = process.env.NODE_ENV === 'development';
    res.status(500).json({
      success: false,
      error: {
        type: ErrorType.INTERNAL,
        message: 'Internal server error',
        ...(isDev && { stack: err.stack }),
      },
    });
  }
}

// Format Zod validation errors
export function formatZodError(error: ZodError): Record<string, string[]> {
  const formattedErrors: Record<string, string[]> = {};

  error.errors.forEach(err => {
    const path = err.path.join('.');
    if (!formattedErrors[path]) {
      formattedErrors[path] = [];
    }
    formattedErrors[path].push(err.message);
  });

  return formattedErrors;
}

// Global error handler middleware
export function errorHandler(err: Error, _req: Request, res: Response, _next: NextFunction): void {
  console.error('Error:', err);

  // Handle payload too large errors (for photo uploads)
  if (err.message && err.message.includes('PayloadTooLargeError')) {
    res.status(413).json({
      error: {
        type: ErrorType.BAD_REQUEST,
        message: 'File too large',
        details: 'Photo must be less than 5MB. Please compress your image and try again.',
      },
    });
    return;
  }

  // Handle request entity too large
  if (err.message && err.message.includes('request entity too large')) {
    res.status(413).json({
      error: {
        type: ErrorType.BAD_REQUEST,
        message: 'Request too large',
        details: 'Photo must be less than 5MB. Please compress your image and try again.',
      },
    });
    return;
  }

  // Handle Zod validation errors
  if (err instanceof ZodError) {
    const formattedErrors = formatZodError(err);
    res.status(400).json({
      error: {
        type: ErrorType.VALIDATION,
        message: 'Validation error',
        details: formattedErrors,
      },
    });
    return;
  }

  // Handle custom AppError
  if (err instanceof AppError) {
    res.status(err.statusCode).json({
      error: {
        type: err.type,
        message: err.message,
        details: err.details,
      },
    });
    return;
  }

  // Handle other errors
  const isDev = process.env.NODE_ENV === 'development';
  res.status(500).json({
    error: {
      type: ErrorType.INTERNAL,
      message: 'Internal server error',
      ...(isDev && { stack: err.stack }),
    },
  });
}

// Validation middleware using Zod schemas
export function validateRequest(schema: any, property: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, _res: Response, next: NextFunction) => {
    try {
      if (property === 'query') {
        // For query parameters, just validate without replacing
        schema.parse(req[property]);
      } else {
        // For body and params, we can safely replace
        req[property] = schema.parse(req[property]);
      }
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        next(AppError.validation('Validation error', formatZodError(error)));
      } else {
        next(error);
      }
    }
  };
}
