{"name": "naqa<PERSON>-server", "version": "1.0.0", "main": "dist/index.js", "license": "MIT", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "build": "tsc", "lint": "eslint .", "lint:fix": "eslint . --ext .ts --fix", "lint:fix-all": "eslint . --fix", "type-check": "tsc --noEmit", "prepare": "husky install", "validate": "yarn lint && yarn type-check", "precommit": "yarn lint:fix && yarn type-check", "reset-db": "ts-node src/scripts/reset-db.ts", "test-db": "ts-node src/scripts/test-db-connection.ts", "list-users": "ts-node src/scripts/list-users.ts", "user-stats": "ts-node src/scripts/user-stats.ts", "unapprove-user": "ts-node src/scripts/unapprove-user.ts", "list-admins": "ts-node src/scripts/list-admins.ts", "add-admin": "ts-node src/scripts/add-admin.ts"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@prisma/client": "^6.8.2", "@types/pdfkit": "^0.14.0", "@types/qrcode": "^1.5.5", "axios": "^1.10.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.3", "pdfkit": "^0.15.0", "pg": "^8.16.0", "prisma": "^6.8.1", "qrcode": "^1.5.2", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.4", "@types/nodemailer": "^6.4.17", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-promise": "^7.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^2.8.8", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}