/* eslint-env jest */
import { NotificationService } from "../services/NotificationService";
import { NotificationPriority, NotificationType } from "../types/models";
// Declare Jest globals for TypeScript without relying on @types/jest (keeps file self-contained during CI setup)

declare const jest: any,
  describe: any,
  it: any,
  expect: any,
  beforeEach: any,
  afterEach: any;

// @ts-nocheck

// Mock implementation of PrismaClient with only the methods we need for tests
const createMockPrisma = () => {
  return {
    notification: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
    },
    notificationPreference: {
      findUnique: jest.fn(),
      create: jest.fn(),
      upsert: jest.fn(),
    },
    // $transaction is used for bulk create
    $transaction: jest.fn(),
  } as any;
};

describe("NotificationService", () => {
  let mockPrisma: ReturnType<typeof createMockPrisma>;
  let service: NotificationService;

  beforeEach(() => {
    mockPrisma = createMockPrisma();
    service = new NotificationService(mockPrisma as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("createNotificationEnhanced should call prisma.notification.create and return the created object", async () => {
    const input = {
      userId: "user-1",
      shipmentId: "shipment-1",
      type: NotificationType.SHIPMENT_CREATED,
      title: "📦 Shipment Created",
      message: "Your shipment has been created",
      priority: NotificationPriority.NORMAL,
    } as const;

    const fakeNotification = {
      id: "notif-1",
      user_id: input.userId,
      shipment_id: input.shipmentId,
      notification_type: input.type,
      title: input.title,
      message: input.message,
      priority: input.priority,
      read: false,
      read_at: null,
      metadata: null,
      expires_at: null,
      created_at: new Date(),
      updated_at: new Date(),
    };

    mockPrisma.notification.create.mockResolvedValue(fakeNotification);

    const result = await service.createNotificationEnhanced(input);

    expect(mockPrisma.notification.create).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          user_id: input.userId,
          shipment_id: input.shipmentId,
          notification_type: input.type,
          title: input.title,
          message: input.message,
          priority: input.priority,
        }),
      })
    );

    expect(result).toEqual(fakeNotification);
  });

  it("getNotificationsByUserId should forward filters and pagination to prisma.findMany", async () => {
    const fakeNotifications: any[] = [];

    mockPrisma.notification.findMany.mockResolvedValue(fakeNotifications);

    const filters = {
      read: false,
      type: NotificationType.SHIPMENT_CREATED,
    } as const;
    const pagination = { skip: 20, take: 10 } as const;

    const result = await service.getNotificationsByUserId(
      "user-1",
      filters,
      pagination
    );

    expect(mockPrisma.notification.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expect.objectContaining({
          user_id: "user-1",
          read: false,
          notification_type: NotificationType.SHIPMENT_CREATED,
        }),
        skip: pagination.skip,
        take: pagination.take,
      })
    );

    expect(result).toBe(fakeNotifications);
  });

  it("markNotificationAsRead should update a notification", async () => {
    const fakeNotification = {
      id: "notif-1",
      read: true,
      read_at: new Date(),
    } as any;

    mockPrisma.notification.update.mockResolvedValue(fakeNotification);

    const result = await service.markNotificationAsRead("notif-1");

    expect(mockPrisma.notification.update).toHaveBeenCalledWith({
      where: { id: "notif-1" },
      data: { read: true, read_at: expect.any(Date) },
    });

    expect(result).toBe(fakeNotification);
  });

  it("markAllNotificationsAsRead should call updateMany with correct where clause", async () => {
    mockPrisma.notification.updateMany.mockResolvedValue({ count: 3 });

    await service.markAllNotificationsAsRead("user-1");

    expect(mockPrisma.notification.updateMany).toHaveBeenCalledWith({
      where: { user_id: "user-1", read: false },
      data: { read: true, read_at: expect.any(Date) },
    });
  });

  it("deleteExpiredNotifications should delete old notifications and return count", async () => {
    mockPrisma.notification.deleteMany.mockResolvedValue({ count: 5 });

    const count = await service.deleteExpiredNotifications();

    expect(mockPrisma.notification.deleteMany).toHaveBeenCalledWith({
      where: { expires_at: { lt: expect.any(Date) } },
    });

    expect(count).toBe(5);
  });
});
