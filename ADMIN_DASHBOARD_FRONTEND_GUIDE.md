# Admin Dashboard Frontend Development Guide

## Overview
This document provides comprehensive API documentation for building the admin dashboard frontend. All endpoints require admin authentication via <PERSON><PERSON><PERSON> Bearer token.

## Base Configuration
- **Base URL**: `http://localhost:8000/api/admin`
- **Authentication**: JWT Bearer Token
- **Content-Type**: `application/json`

## Authentication Header
```javascript
headers: {
  'Authorization': `Bearer ${adminToken}`,
  'Content-Type': 'application/json'
}
```

## 📊 Dashboard & Analytics Pages

### Main Dashboard
**GET** `/dashboard`
```javascript
// Response includes comprehensive metrics
{
  "success": true,
  "data": {
    "admin_info": { /* admin details */ },
    "dashboard_data": {
      "total_users": 150,
      "total_shipments": 1200,
      "pending_approvals": 25,
      "active_shipments": 45
    }
  }
}
```

### System Overview Analytics
**GET** `/analytics/overview`
```javascript
// System-wide metrics and KPIs
{
  "success": true,
  "data": {
    "system_health": "healthy",
    "performance_metrics": { /* performance data */ },
    "usage_statistics": { /* usage stats */ }
  }
}
```

### User Analytics
**GET** `/analytics/users`
```javascript
// User registration trends, activity patterns
{
  "success": true,
  "data": {
    "user_growth": { /* growth charts data */ },
    "user_types_distribution": { /* pie chart data */ },
    "activity_patterns": { /* activity heatmaps */ }
  }
}
```

### Shipment Analytics
**GET** `/analytics/shipments`
```javascript
// Shipment volume, success rates, geographic distribution
{
  "success": true,
  "data": {
    "shipment_volume": { /* volume over time */ },
    "success_rates": { /* completion rates */ },
    "geographic_data": { /* map visualization data */ }
  }
}
```

### Performance Metrics
**GET** `/analytics/performance`
```javascript
// System performance, response times, error rates
{
  "success": true,
  "data": {
    "response_times": { /* API performance */ },
    "error_rates": { /* error statistics */ },
    "uptime_metrics": { /* system availability */ }
  }
}
```

### System Health Check
**GET** `/system/health`
```javascript
// Real-time system status
{
  "success": true,
  "data": {
    "database": "healthy",
    "email_service": "healthy",
    "external_apis": "healthy",
    "disk_usage": "75%",
    "memory_usage": "60%"
  }
}
```

## 👥 User Management Pages

### Users List Page
**GET** `/users`
**Query Parameters:**
- `page`: Page number (default: 0)
- `limit`: Items per page (default: 20, max: 100)
- `search`: Search by name or email
- `user_type`: Filter by CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR
- `status`: Filter by ACTIVE, PENDING, SUSPENDED
- `approval_status`: Filter by APPROVED, PENDING, REJECTED

```javascript
// GET /users?page=0&limit=20&user_type=ACCESS_OPERATOR&status=ACTIVE
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "uuid",
        "name": "John Doe",
        "email": "<EMAIL>",
        "user_type": "ACCESS_OPERATOR",
        "status": "ACTIVE",
        "email_verified": true,
        "approval_status": "PENDING", // For operators
        "created_at": "2025-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "total": 150,
      "page": 0,
      "limit": 20,
      "total_pages": 8
    }
  }
}
```

### User Details Page
**GET** `/users/:id`
```javascript
// Detailed user information with related data
{
  "success": true,
  "data": {
    "user": { /* complete user profile */ },
    "operator_details": { /* AO/CO specific data */ },
    "recent_shipments": [ /* last 10 shipments */ ],
    "activity_summary": { /* user activity stats */ }
  }
}
```

### Update User Status
**PUT** `/users/status`
```javascript
// Request
{
  "userId": "user-uuid",
  "status": "SUSPENDED" // ACTIVE, SUSPENDED
}

// Response
{
  "success": true,
  "message": "User status updated to SUSPENDED",
  "data": {
    "user": {
      "id": "user-uuid",
      "status": "SUSPENDED"
    }
  }
}
```

### Comprehensive User Management (NEW)
**PUT** `/users/management`
```javascript
// Update both status and approval in one call
// Request
{
  "userId": "user-uuid",
  "status": "ACTIVE",        // Optional: ACTIVE, SUSPENDED
  "approved": true,          // Optional: true/false (for operators only)
  "reason": "Profile verified" // Optional: reason for changes
}

// Response
{
  "success": true,
  "message": "User management updated successfully",
  "data": {
    "user": { /* updated user data */ },
    "updates": { /* what was changed */ }
  }
}
```

### Bulk Operations
**POST** `/users/bulk-approve`
```javascript
// Request
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}

// Response
{
  "success": true,
  "message": "Bulk approval completed. 3 approved, 0 failed.",
  "data": {
    "successful": 3,
    "failed": 0,
    "total": 3
  }
}
```

**POST** `/users/bulk-reject`
```javascript
// Request
{
  "user_ids": ["uuid1", "uuid2"]
}

// Response
{
  "success": true,
  "message": "2 operators rejected successfully",
  "data": {
    "rejected_count": 2
  }
}
```

### User Activity History
**GET** `/users/:id/activity`
```javascript
// Paginated user activity logs
{
  "success": true,
  "data": {
    "activities": [
      {
        "action": "USER_STATUS_CHANGED",
        "timestamp": "2025-01-01T10:00:00.000Z",
        "details": { /* action details */ }
      }
    ],
    "pagination": { /* pagination info */ }
  }
}
```

### Send Notification to User
**POST** `/users/:id/notify`
```javascript
// Request
{
  "title": "Important Notice",
  "message": "Your account has been updated.",
  "type": "SYSTEM",
  "priority": "HIGH"
}

// Response
{
  "success": true,
  "message": "Notification sent successfully"
}
```

## 👨‍💼 Admin Management Pages

### Admins List
**GET** `/admins`
```javascript
{
  "success": true,
  "data": {
    "admins": [
      {
        "id": "uuid",
        "name": "Admin Name",
        "email": "<EMAIL>",
        "role": "ADMIN",
        "status": "ACTIVE",
        "created_at": "2025-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### Create New Admin
**POST** `/admins`
```javascript
// Request
{
  "name": "New Admin",
  "email": "<EMAIL>",
  "password": "securepassword",
  "role": "ADMIN" // ADMIN, AUDITOR, SUPPORT
}

// Response
{
  "success": true,
  "message": "Admin created successfully",
  "data": { /* new admin data */ }
}
```

### Admin Details
**GET** `/admins/:id`
```javascript
{
  "success": true,
  "data": {
    "admin": { /* admin profile */ },
    "auditLogs": [ /* recent actions */ ],
    "loginAttempts": [ /* login history */ ],
    "_count": {
      "auditLogs": 150,
      "loginAttempts": 45
    }
  }
}
```

### Update Admin
**PUT** `/admins/:id`
```javascript
// Request
{
  "name": "Updated Name",
  "role": "SUPPORT"
}

// Response
{
  "success": true,
  "message": "Admin updated successfully",
  "data": { /* updated admin */ }
}
```

### Change Admin Status
**POST** `/admins/:id/status`
```javascript
// Request
{
  "id": "admin-uuid",
  "status": "SUSPENDED"
}

// Response
{
  "success": true,
  "message": "Admin suspended successfully"
}
```

## 📦 Shipment Management Pages

### Shipments List
**GET** `/shipments`
**Query Parameters:**
- `page`, `limit`: Pagination
- `status`: Filter by shipment status
- `search`: Search by tracking number or customer
- `date_from`, `date_to`: Date range filter

```javascript
{
  "success": true,
  "data": {
    "shipments": [
      {
        "id": "uuid",
        "tracking_number": "SH123456",
        "status": "IN_TRANSIT",
        "customer_name": "John Doe",
        "origin_ao": "Origin Point",
        "dest_ao": "Destination Point",
        "created_at": "2025-01-01T00:00:00.000Z"
      }
    ],
    "pagination": { /* pagination info */ }
  }
}
```

### Shipment Details
**GET** `/shipments/:id`
```javascript
{
  "success": true,
  "data": {
    "shipment": { /* complete shipment data */ },
    "tracking_history": [ /* status changes */ ],
    "photos": [ /* shipment photos */ ],
    "qr_labels": [ /* QR codes */ ]
  }
}
```

### Update Shipment Status
**POST** `/shipments/:id/status`
```javascript
// Request
{
  "status": "CANCELLED",
  "reason": "Customer request"
}

// Response
{
  "success": true,
  "message": "Shipment status updated successfully"
}
```

### Bulk Cancel Shipments
**POST** `/shipments/bulk-cancel`
```javascript
// Request
{
  "shipment_ids": ["uuid1", "uuid2"],
  "reason": "System maintenance"
}

// Response
{
  "success": true,
  "message": "2 shipments cancelled successfully"
}
```

### Expired Shipments
**GET** `/shipments/expired`
```javascript
{
  "success": true,
  "data": {
    "expired_shipments": [ /* expired shipments list */ ],
    "count": 15
  }
}
```

### Reassign Shipment
**POST** `/shipments/:id/reassign`
```javascript
// Request
{
  "new_car_operator_id": "co-uuid",
  "reason": "Original CO unavailable"
}

// Response
{
  "success": true,
  "message": "Shipment reassigned successfully"
}
```

## ⚙️ System Settings Pages

### Get System Settings
**GET** `/settings`
```javascript
{
  "success": true,
  "data": {
    "min_distance_km": 2.0,
    "max_shipments_per_day": 10,
    "max_shipments_per_user": 100,
    "max_pending_shipments": 5,
    "require_photo_proof": true,
    "max_failed_logins": 5,
    "review_period_hours": 24,
    "enable_2fa": true,
    "gps_tolerance_meters": 50
  }
}
```

### Update System Settings
**PUT** `/settings`
```javascript
// Request
{
  "min_distance_km": 3.0,
  "max_shipments_per_day": 15,
  "require_photo_proof": false
}

// Response
{
  "success": true,
  "message": "System settings updated successfully",
  "data": { /* updated settings */ }
}
```

### Reset Settings to Defaults
**POST** `/settings/reset`
```javascript
{
  "success": true,
  "message": "System settings reset to defaults",
  "data": { /* default settings */ }
}
```

### Settings History
**GET** `/settings/history`
```javascript
{
  "success": true,
  "data": {
    "history": [
      {
        "changed_at": "2025-01-01T10:00:00.000Z",
        "changed_by": "admin-uuid",
        "changes": { /* what was changed */ }
      }
    ]
  }
}
```

## 🔔 Notification Management Pages

### All Notifications
**GET** `/notifications`
```javascript
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "title": "System Maintenance",
        "message": "Scheduled maintenance tonight",
        "type": "SYSTEM",
        "priority": "HIGH",
        "sent_at": "2025-01-01T10:00:00.000Z"
      }
    ]
  }
}
```

### Broadcast Notification
**POST** `/notifications/broadcast`
```javascript
// Request
{
  "title": "Important Announcement",
  "message": "New features available",
  "type": "ANNOUNCEMENT",
  "priority": "MEDIUM",
  "target_user_types": ["CUSTOMER", "ACCESS_OPERATOR"] // Optional
}

// Response
{
  "success": true,
  "message": "Notification broadcasted successfully",
  "data": {
    "recipients_count": 150
  }
}
```

### Notification Templates
**GET** `/notifications/templates`
```javascript
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "uuid",
        "name": "Welcome Message",
        "subject": "Welcome to Platform",
        "body": "Welcome {{user_name}}...",
        "type": "EMAIL"
      }
    ]
  }
}
```

### Create Notification Template
**POST** `/notifications/templates`
```javascript
// Request
{
  "name": "Account Suspended",
  "subject": "Account Status Update",
  "body": "Your account has been suspended due to {{reason}}",
  "type": "EMAIL"
}

// Response
{
  "success": true,
  "message": "Template created successfully"
}
```

### Notification Statistics
**GET** `/notifications/stats`
```javascript
{
  "success": true,
  "data": {
    "total_sent": 1500,
    "delivery_rate": 98.5,
    "open_rate": 75.2,
    "click_rate": 12.8,
    "by_type": {
      "EMAIL": 1200,
      "PUSH": 300
    }
  }
}
```

## 🔒 Audit & Security Pages

### Audit Logs
**GET** `/audit-logs`
**Query Parameters:**
- `page`, `limit`: Pagination
- `action`: Filter by action type
- `user_id`: Filter by user
- `admin_id`: Filter by admin
- `date_from`, `date_to`: Date range

```javascript
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "uuid",
        "action": "USER_STATUS_CHANGED_BY_ADMIN",
        "user_id": "user-uuid",
        "admin_id": "admin-uuid",
        "details": { /* action details */ },
        "created_at": "2025-01-01T10:00:00.000Z"
      }
    ],
    "pagination": { /* pagination info */ }
  }
}
```

### Login Attempts
**GET** `/security/login-attempts`
```javascript
{
  "success": true,
  "data": {
    "attempts": [
      {
        "id": "uuid",
        "admin_id": "admin-uuid",
        "ip_address": "***********",
        "successful": true,
        "attempted_at": "2025-01-01T10:00:00.000Z"
      }
    ]
  }
}
```

### Security Events
**GET** `/security/events`
```javascript
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "uuid",
        "event_type": "FAILED_LOGIN",
        "severity": "MEDIUM",
        "description": "Multiple failed login attempts",
        "created_at": "2025-01-01T10:00:00.000Z"
      }
    ]
  }
}
```

### Lock User Account
**POST** `/security/lock-user`
```javascript
// Request
{
  "user_id": "user-uuid",
  "reason": "Suspicious activity detected"
}

// Response
{
  "success": true,
  "message": "User account locked successfully"
}
```

### Unlock User Account
**POST** `/security/unlock-user`
```javascript
// Request
{
  "user_id": "user-uuid",
  "reason": "Issue resolved"
}

// Response
{
  "success": true,
  "message": "User account unlocked successfully"
}
```

## 📊 Reporting & Export Pages

### User Reports
**GET** `/reports/users`
**Query Parameters:**
- `format`: json, csv, excel
- `date_from`, `date_to`: Date range
- `user_type`: Filter by user type

```javascript
{
  "success": true,
  "data": {
    "report": {
      "total_users": 150,
      "new_registrations": 25,
      "active_users": 120,
      "by_type": {
        "CUSTOMER": 100,
        "ACCESS_OPERATOR": 30,
        "CAR_OPERATOR": 20
      }
    }
  }
}
```

### Shipment Reports
**GET** `/reports/shipments`
```javascript
{
  "success": true,
  "data": {
    "report": {
      "total_shipments": 1200,
      "completed": 1000,
      "cancelled": 50,
      "in_progress": 150,
      "success_rate": 83.3
    }
  }
}
```

### System Reports
**GET** `/reports/system`
```javascript
{
  "success": true,
  "data": {
    "report": {
      "uptime": "99.9%",
      "avg_response_time": "120ms",
      "error_rate": "0.1%",
      "peak_usage_hours": ["09:00", "17:00"]
    }
  }
}
```

### Export Data
**POST** `/export/users`
```javascript
// Request
{
  "format": "excel", // csv, excel, json
  "filters": {
    "user_type": "ACCESS_OPERATOR",
    "status": "ACTIVE"
  }
}

// Response
{
  "success": true,
  "message": "Export initiated",
  "data": {
    "export_id": "export-uuid",
    "estimated_completion": "2025-01-01T10:05:00.000Z"
  }
}
```

### Download Export
**GET** `/export/:id/download`
```javascript
// Returns file download or status
{
  "success": true,
  "data": {
    "status": "completed", // pending, processing, completed, failed
    "download_url": "/api/admin/export/uuid/download",
    "file_size": "2.5MB",
    "expires_at": "2025-01-02T10:00:00.000Z"
  }
}
```

## 🎨 Frontend Implementation Tips

### State Management
```javascript
// Recommended state structure
const adminState = {
  auth: { token, admin, permissions },
  dashboard: { metrics, charts },
  users: { list, filters, pagination, selected },
  shipments: { list, filters, selected },
  notifications: { list, unread_count },
  settings: { system, user_preferences },
  exports: { pending, completed }
};
```

### Error Handling
```javascript
// Standard error response format
{
  "success": false,
  "error": "Validation error",
  "message": "Invalid input data",
  "details": {
    "field_name": ["Error message"]
  }
}
```

### Pagination Component
```javascript
// Standard pagination response
{
  "pagination": {
    "total": 150,
    "page": 0,
    "limit": 20,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

### Real-time Updates
Consider implementing WebSocket connections for:
- Real-time notifications
- Live dashboard metrics
- User activity monitoring
- System health status

### Recommended Pages Structure
```
/admin
├── /dashboard          # Main dashboard
├── /analytics         # Analytics pages
│   ├── /overview
│   ├── /users
│   ├── /shipments
│   └── /performance
├── /users             # User management
│   ├── /list
│   ├── /details/:id
│   └── /bulk-actions
├── /admins            # Admin management
├── /shipments         # Shipment management
├── /settings          # System settings
├── /notifications     # Notification center
├── /security          # Security & audit
│   ├── /audit-logs
│   ├── /login-attempts
│   └── /events
└── /reports           # Reports & exports
```

## 🔐 Authentication Flow
1. Admin login → Receive JWT token
2. Store token securely (httpOnly cookie recommended)
3. Include token in all API requests
4. Handle token expiration (8 hours)
5. Implement refresh token mechanism if needed

## 📱 Responsive Design Considerations
- Mobile-first approach for admin dashboard
- Touch-friendly controls for mobile admins
- Collapsible sidebar navigation
- Responsive data tables with horizontal scroll
- Modal dialogs for quick actions

This documentation provides all the necessary endpoints and data structures needed to build a comprehensive admin dashboard frontend.
