/* eslint-disable no-console */
import path from 'path';

import cors from 'cors';
import dotenv from 'dotenv';
import express, { Express, Request, Response } from 'express';
import helmet from 'helmet';
import morgan from 'morgan';

import routes from './routes';
import { ServiceFactory } from './services/ServiceFactory';
import { errorHandler } from './utils/errors';
import { prisma } from './utils/prisma';

// Load environment variables
// Try to load from .env.local first, then fall back to .env
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

// Initialize service factory
ServiceFactory.initialize(prisma);

// Start scheduled jobs
const scheduledJobService = ServiceFactory.getScheduledJobService();
scheduledJobService.startExpiredShipmentChecker();

const app: Express = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
// Configure body parser with increased limits for photo uploads
// Base64 encoding increases size by ~33%, so 5MB image becomes ~6.7MB in base64
app.use(express.json({
  limit: '8mb'
}));
app.use(express.urlencoded({
  extended: true,
  limit: '8mb',
  parameterLimit: 50000
}));

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

// API routes
app.use('/api', routes);

// Health check route
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({ status: 'ok' });
});

// Home route
app.get('/', (_req: Request, res: Response) => {
  res.send('Shipment Relay Platform API - Welcome!');
});

// Error handling middleware
app.use(errorHandler);

// Start server
const server = app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM signal received: closing HTTP server');

  // Stop scheduled jobs
  scheduledJobService.stopExpiredShipmentChecker();

  await prisma.$disconnect();
  server.close(() => {
    console.log('HTTP server closed');
  });
});

export default app;
