// User-related types
export enum UserType {
  CUSTOMER = "CUSTOMER",
  ACCESS_OPERATOR = "ACCESS_OPERATOR",
  CAR_OPERATOR = "CAR_OPERATOR",
  ADMIN = "ADMIN",
}

export enum UserStatus {
  ACTIVE = "ACTIVE",
  PENDING = "PENDING",
  SUSPENDED = "SUSPENDED",
  INACTIVE = "INACTIVE",
}

export interface User {
  id: string;
  name: string;
  email: string;
  password_hash: string;
  phone: string;
  user_type: UserType;
  status: UserStatus;
  email_verified: boolean;
  reset_password_token?: string;
  reset_token_expires?: Date;
  created_at: Date;
  updated_at: Date;
  updated_by?: string;
}

export interface EmailVerification {
  id: string;
  user_id: string;
  token: string;
  expires_at: Date;
  created_at: Date;
  used: boolean;
}

export interface PasswordReset {
  id: string;
  user_id: string;
  token: string;
  expires_at: Date;
  created_at: Date;
  used: boolean;
}

export interface AccessOperator extends User {
  business_name: string;
  address: string;
  geo_location: GeoPoint;
  approved: boolean;
  updated_by?: string;
}

export interface CarOperator extends User {
  license_number: string;
  vehicle_info: string;
  approved: boolean;
  updated_by?: string;
}

// Shipment-related types
export enum ShipmentStatus {
  PENDING = "PENDING",
  AWAITING_PICKUP = "AWAITING_PICKUP",
  PICKED_UP_BY_CO = "PICKED_UP_BY_CO",
  IN_TRANSIT = "IN_TRANSIT",
  ARRIVED_AT_DESTINATION = "ARRIVED_AT_DESTINATION",
  READY_FOR_DELIVERY = "READY_FOR_DELIVERY",
  DELIVERED = "DELIVERED",
  CANCELLED = "CANCELLED",
}

export enum CancellationReason {
  USER_CANCELLED = "USER_CANCELLED",
  SYSTEM_EXPIRED = "SYSTEM_EXPIRED",
  ADMIN_CANCELLED = "ADMIN_CANCELLED",
}

export interface Shipment {
  id: string;
  customer_id: string;
  origin_ao_id: string | null;
  dest_ao_id: string | null;

  status: ShipmentStatus;
  weight: number;
  size: string;
  description: string;
  pickup_code: string;
  receiver_name: string;
  receiver_phone: string;
  cancellation_reason: CancellationReason | null;
  cancelled_at: Date | null;
  expires_at: Date | null;
  created_at: Date;
  updated_at: Date;
  updated_by?: string;
  photos?: ShipmentPhoto[];
}

// QR Label types
export enum QRLabelStatus {
  UNUSED = "UNUSED",
  ASSIGNED = "ASSIGNED",
  USED = "USED",
}

export interface ShipmentQRLabel {
  id: string;
  ao_id: string;
  qr_value: string;
  status: QRLabelStatus;
  shipment_id: string | null;
  assigned_at: Date | null;
  created_at: Date;
  updated_at: Date;
  updated_by?: string;
}

export enum ShipmentPhotoAction {
  QR_ASSIGNMENT = "QR_ASSIGNMENT",
  DROPOFF = "DROPOFF",
  PICKUP = "PICKUP",
  ARRIVAL = "ARRIVAL",
  DELIVERY = "DELIVERY",
}

export interface ShipmentPhoto {
  id: string;
  shipment_id: string;
  user_id: string;
  photo_url: string;
  action: ShipmentPhotoAction;
  notes?: string;
  latitude?: number;
  longitude?: number;
  created_at: Date;
}

// Notification types
export enum NotificationType {
  SHIPMENT_CREATED = "SHIPMENT_CREATED",
  SHIPMENT_ASSIGNED_QR = "SHIPMENT_ASSIGNED_QR",
  SHIPMENT_DROPPED_OFF = "SHIPMENT_DROPPED_OFF",
  SHIPMENT_PICKED_UP = "SHIPMENT_PICKED_UP",
  SHIPMENT_IN_TRANSIT = "SHIPMENT_IN_TRANSIT",
  SHIPMENT_ARRIVED = "SHIPMENT_ARRIVED",
  SHIPMENT_READY_FOR_DELIVERY = "SHIPMENT_READY_FOR_DELIVERY",
  SHIPMENT_DELIVERED = "SHIPMENT_DELIVERED",
  SHIPMENT_CANCELLED = "SHIPMENT_CANCELLED",
  SHIPMENT_EXPIRED = "SHIPMENT_EXPIRED",
  SHIPMENT_STATUS_CHANGED = "SHIPMENT_STATUS_CHANGED",
  QR_CODE_ASSIGNED = "QR_CODE_ASSIGNED",
  PACKAGE_READY_FOR_PICKUP = "PACKAGE_READY_FOR_PICKUP",
  DELIVERY_REMINDER = "DELIVERY_REMINDER",
  SYSTEM_ALERT = "SYSTEM_ALERT",
}

export enum NotificationPriority {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH",
  URGENT = "URGENT",
}

export interface Notification {
  id: string;
  user_id: string;
  shipment_id: string | null;
  notification_type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  read: boolean;
  read_at: Date | null;
  metadata: Record<string, any> | null;
  expires_at: Date | null;
  created_at: Date;
  updated_at: Date;
}

export interface NotificationPreference {
  id: string;
  user_id: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  push_notifications: boolean;
  shipment_created: boolean;
  shipment_status_change: boolean;
  qr_assignment: boolean;
  package_ready: boolean;
  delivery_completed: boolean;
  preferred_language: Language;
  email_format: EmailFormat;
  created_at: Date;
  updated_at: Date;
}

// Audit log types
export interface AuditLog {
  id: string;
  user_id: string;
  action: string;
  shipment_id: string | null;
  details: Record<string, any>;
  created_at: Date;
}

// Language and Email Format types
export enum Language {
  ENGLISH = "ENGLISH",
  ARABIC = "ARABIC",
}

export enum EmailFormat {
  ENGLISH_ONLY = "ENGLISH_ONLY",
  ARABIC_ONLY = "ARABIC_ONLY",
  BILINGUAL = "BILINGUAL",
}

// Utility types
export interface GeoPoint {
  latitude: number;
  longitude: number;
}
