import express from 'express';

import { AdminController } from '../controllers/AdminController';
import { adminMiddleware } from '../middleware/adminMiddleware';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// ============================================================================
// AUTHENTICATION & PROFILE MANAGEMENT
// ============================================================================

// Admin authentication routes (simplified - no registration or OTP)
router.post('/login', AdminController.loginAdmin);
router.post('/forgot-password', AdminController.forgotPassword);
router.post('/reset-password', AdminController.resetPassword);

// Protected admin profile routes (require authentication)
router.get('/profile', authMiddleware, adminMiddleware, AdminController.getAdminProfile);
router.put('/profile', authMiddleware, adminMiddleware, AdminController.updateAdminProfile);
router.post('/change-password', authMiddleware, adminMiddleware, AdminController.changePassword);
router.post('/logout', authMiddleware, adminMiddleware, AdminController.logoutAdmin);

// ============================================================================
// DASHBOARD & ANALYTICS
// ============================================================================

// Enhanced dashboard with comprehensive metrics
router.get('/dashboard', authMiddleware, adminMiddleware, AdminController.getAdminDashboard);
router.get('/analytics/overview', authMiddleware, adminMiddleware, AdminController.getSystemOverview);
router.get('/analytics/users', authMiddleware, adminMiddleware, AdminController.getUserAnalytics);
router.get('/analytics/shipments', authMiddleware, adminMiddleware, AdminController.getShipmentAnalytics);
router.get('/analytics/performance', authMiddleware, adminMiddleware, AdminController.getPerformanceMetrics);
router.get('/system/health', authMiddleware, adminMiddleware, AdminController.getSystemHealth);

// ============================================================================
// USER MANAGEMENT (ENHANCED)
// ============================================================================

// Enhanced user management with advanced filtering and bulk operations
router.get('/users', authMiddleware, adminMiddleware, AdminController.getAllUsersEnhanced);
router.put('/users/status', authMiddleware, adminMiddleware, AdminController.updateUserStatus);
router.put('/users/management', authMiddleware, adminMiddleware, AdminController.updateUserManagement);
router.get('/users/:id', authMiddleware, adminMiddleware, AdminController.getUserByIdEnhanced);
// Handle incorrect PUT request to approval endpoint
router.put('/users/approval', authMiddleware, adminMiddleware, (_req, res) => {
  res.status(405).json({
    success: false,
    message: "Method not allowed. Use POST /api/admin/users/approval instead.",
    error: {
      type: "METHOD_NOT_ALLOWED",
      details: "This endpoint only accepts POST requests"
    }
  });
});
router.put('/users/:id', authMiddleware, adminMiddleware, AdminController.updateUser);
router.post('/users/bulk-approve', authMiddleware, adminMiddleware, AdminController.bulkApproveOperators);
router.post('/users/bulk-reject', authMiddleware, adminMiddleware, AdminController.bulkRejectOperators);
router.get('/users/:id/activity', authMiddleware, adminMiddleware, AdminController.getUserActivity);
router.post('/users/:id/notify', authMiddleware, adminMiddleware, AdminController.notifyUser);

// Legacy routes for backward compatibility
router.post('/users/approval', authMiddleware, adminMiddleware, AdminController.updateOperatorApproval);

// ============================================================================
// ADMIN MANAGEMENT
// ============================================================================

// Comprehensive admin management
router.get('/admins', authMiddleware, adminMiddleware, AdminController.getAllAdmins);
router.post('/admins', authMiddleware, adminMiddleware, AdminController.createAdmin);
router.get('/admins/:id', authMiddleware, adminMiddleware, AdminController.getAdminById);
router.put('/admins/:id', authMiddleware, adminMiddleware, AdminController.updateAdmin);
router.post('/admins/:id/status', authMiddleware, adminMiddleware, AdminController.changeAdminStatus);
router.delete('/admins/:id', authMiddleware, adminMiddleware, AdminController.deactivateAdmin);

// Legacy route for backward compatibility
router.post('/status', authMiddleware, adminMiddleware, AdminController.changeAdminStatus);

// ============================================================================
// SHIPMENT MANAGEMENT
// ============================================================================

// Comprehensive shipment management for admins
router.get('/shipments', authMiddleware, adminMiddleware, AdminController.getAllShipments);
router.get('/shipments/:id', authMiddleware, adminMiddleware, AdminController.getShipmentById);
router.put('/shipments/:id', authMiddleware, adminMiddleware, AdminController.updateShipment);
router.post('/shipments/:id/status', authMiddleware, adminMiddleware, AdminController.updateShipmentStatus);
router.post('/shipments/bulk-cancel', authMiddleware, adminMiddleware, AdminController.bulkCancelShipments);
router.get('/shipments/expired', authMiddleware, adminMiddleware, AdminController.getExpiredShipments);
router.post('/shipments/:id/reassign', authMiddleware, adminMiddleware, AdminController.reassignShipment);

// Legacy route for backward compatibility
router.get('/shipments/expired-stats', authMiddleware, adminMiddleware, AdminController.getExpiredShipmentStats);

// ============================================================================
// SYSTEM SETTINGS
// ============================================================================

// System configuration management
router.get('/settings', authMiddleware, adminMiddleware, AdminController.getSystemSettings);
router.put('/settings', authMiddleware, adminMiddleware, AdminController.updateSystemSettings);
router.post('/settings/reset', authMiddleware, adminMiddleware, AdminController.resetSystemSettings);
router.get('/settings/history', authMiddleware, adminMiddleware, AdminController.getSettingsHistory);

// ============================================================================
// NOTIFICATION MANAGEMENT
// ============================================================================

// Notification system management
router.get('/notifications', authMiddleware, adminMiddleware, AdminController.getAllNotifications);
router.post('/notifications/broadcast', authMiddleware, adminMiddleware, AdminController.broadcastNotification);
router.get('/notifications/templates', authMiddleware, adminMiddleware, AdminController.getNotificationTemplates);
router.post('/notifications/templates', authMiddleware, adminMiddleware, AdminController.createNotificationTemplate);
router.put('/notifications/templates/:id', authMiddleware, adminMiddleware, AdminController.updateNotificationTemplate);
router.get('/notifications/stats', authMiddleware, adminMiddleware, AdminController.getNotificationStats);

// ============================================================================
// AUDIT & SECURITY
// ============================================================================

// Audit and security monitoring
router.get('/audit-logs', authMiddleware, adminMiddleware, AdminController.getAuditLogs);
router.get('/security/login-attempts', authMiddleware, adminMiddleware, AdminController.getLoginAttempts);
router.get('/security/events', authMiddleware, adminMiddleware, AdminController.getSecurityEvents);
router.post('/security/lock-user', authMiddleware, adminMiddleware, AdminController.lockUser);
router.post('/security/unlock-user', authMiddleware, adminMiddleware, AdminController.unlockUser);

// ============================================================================
// REPORTING & EXPORT
// ============================================================================

// Reporting and data export
router.get('/reports/users', authMiddleware, adminMiddleware, AdminController.getUserReports);
router.get('/reports/shipments', authMiddleware, adminMiddleware, AdminController.getShipmentReports);
router.get('/reports/system', authMiddleware, adminMiddleware, AdminController.getSystemReports);
router.post('/export/users', authMiddleware, adminMiddleware, AdminController.exportUsers);
router.post('/export/shipments', authMiddleware, adminMiddleware, AdminController.exportShipments);
router.get('/export/:id/download', authMiddleware, adminMiddleware, AdminController.downloadExport);

export default router;
