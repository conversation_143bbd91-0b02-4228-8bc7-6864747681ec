/**
 * Arabic Email Templates
 *
 * This file contains all Arabic email templates used in the application.
 * Each template is a function that returns HTML content with proper RTL support.
 */

/**
 * Base Arabic email template with RTL styling
 * @param content The content to include in the template
 * @returns HTML string with RTL support
 */
export function baseArabicTemplate(content: string): string {
  return `
    <div style="font-family: 'Se<PERSON>e UI', Tahoma, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px; background-color: #ffffff; direction: rtl; text-align: right;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333333; margin: 0;">منصة نقل الشحنات</h1>
      </div>
      ${content}
      <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #eaeaea; color: #666666; font-size: 12px; text-align: center;">
        <p>© ${new Date().getFullYear()} منصة نقل الشحنات. جميع الحقوق محفوظة.</p>
        <p>إذا كان لديك أي أسئلة، يرجى التواصل مع فريق الدعم الفني.</p>
      </div>
    </div>
  `;
}

/**
 * Bilingual base template (Arabic + English)
 * @param arabicContent Arabic content
 * @param englishContent English content
 * @returns HTML string with both languages
 */
export function bilingualTemplate(arabicContent: string, englishContent: string): string {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px; background-color: #ffffff;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #333333; margin: 0; direction: rtl;">منصة نقل الشحنات</h1>
        <h1 style="color: #333333; margin: 5px 0 0 0;">Shipment Relay Platform</h1>
      </div>

      <!-- Arabic Content -->
      <div style="direction: rtl; text-align: right; margin-bottom: 30px; padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
        ${arabicContent}
      </div>

      <!-- English Content -->
      <div style="direction: ltr; text-align: left; margin-bottom: 30px; padding: 20px; background-color: #f5f5f5; border-radius: 5px;">
        ${englishContent}
      </div>

      <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #eaeaea; color: #666666; font-size: 12px; text-align: center;">
        <p style="direction: rtl;">© ${new Date().getFullYear()} منصة نقل الشحنات. جميع الحقوق محفوظة.</p>
        <p>© ${new Date().getFullYear()} Shipment Relay Platform. All rights reserved.</p>
        <p style="direction: rtl;">إذا كان لديك أي أسئلة، يرجى التواصل مع فريق الدعم الفني.</p>
        <p>If you have any questions, please contact our support team.</p>
      </div>
    </div>
  `;
}

/**
 * Modern bilingual template with Cairo font for Arabic and Roboto Condensed for English
 * @param arabicContent Arabic content
 * @param englishContent English content
 * @param primaryColor Primary brand color (default: #4CAF50 - Green)
 * @param secondaryColor Secondary brand color (default: #2196F3 - Blue)
 * @returns HTML string with modern gradient layout
 */
export function modernBilingualTemplate(
  arabicContent: string,
  englishContent: string,
  primaryColor: string = '#4CAF50',
  secondaryColor: string = '#2196F3'
): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@400;700&display=swap" rel="stylesheet">
      <style>
        /* Ensure fonts are loaded with proper fallbacks */
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@400;700&display=swap');

        .arabic-text {
          font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
          direction: rtl;
          text-align: right;
        }
        .english-text {
          font-family: 'Roboto Condensed', 'Arial', 'Helvetica', sans-serif !important;
          direction: ltr;
          text-align: left;
        }

        /* Ensure proper font rendering */
        * {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      </style>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f5f5f5;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">

        <!-- Header with green and blue gradient -->
        <div style="background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%); padding: 20px; text-align: center;">
          <div style="color: white; font-size: 28px; font-weight: 700; letter-spacing: 2px;" class="english-text">
            NAQALAT
          </div>
          <div style="color: white; font-size: 16px; font-weight: 400; margin-top: 5px;" class="arabic-text">
            نقلات
          </div>
        </div>

        <!-- Combined Content Section -->
        <div style="padding: 30px 20px;">
          ${arabicContent}
          ${englishContent}
        </div>

        <!-- Footer -->
        <div style="background-color: #f8f8f8; padding: 20px; text-align: center; border-top: 1px solid #e0e0e0;">
          <div style="margin-bottom: 10px;">
            <span style="font-size: 12px; color: #666; direction: rtl;" class="arabic-text">
              © ${new Date().getFullYear()} نقلات. جميع الحقوق محفوظة.
            </span>
          </div>
          <div style="margin-bottom: 15px;">
            <span style="font-size: 12px; color: #666;" class="english-text">
              © ${new Date().getFullYear()} NAQALAT. All rights reserved.
            </span>
          </div>

          <div style="border-top: 1px solid #e0e0e0; padding-top: 15px;">
            <div style="margin-bottom: 5px;">
              <span style="font-size: 11px; color: #888; direction: rtl;" class="arabic-text">
                للاستفسارات والدعم الفني
              </span>
            </div>
            <div>
              <span style="font-size: 11px; color: #888;" class="english-text">
                For inquiries and technical support
              </span>
            </div>
          </div>
        </div>

      </div>
    </body>
    </html>
  `;
}

/**
 * Alias for backward compatibility - now uses green/blue gradient
 */
export function dhlStyleBilingualTemplate(
  arabicContent: string,
  englishContent: string,
  primaryColor: string = '#4CAF50'
): string {
  return modernBilingualTemplate(arabicContent, englishContent, primaryColor, '#2196F3');
}

/**
 * Arabic welcome email template
 * @param name User's name
 * @param userType User's type (CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR)
 * @returns HTML string
 */
export function welcomeEmailTemplateArabic(name: string, userType: string): string {
  const userTypeArabic = getUserTypeInArabic(userType);
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">مرحباً بك في منصة نقل الشحنات!</h2>
    <p>مرحباً ${name}،</p>
    <p>شكراً لك على التسجيل كـ ${userTypeArabic} في منصتنا.</p>
    <p>نحن متحمسون لانضمامك إلينا ونتطلع إلى تقديم تجربة سلسة لك.</p>
    <p>إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، لا تتردد في التواصل مع فريق الدعم الفني.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic email verification template with OTP
 * @param name User's name
 * @param otp One-time password
 * @returns HTML string
 */
export function verificationOTPEmailTemplateArabic(name: string, otp: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">التحقق من البريد الإلكتروني مطلوب</h2>
    <p>مرحباً ${name}،</p>
    <p>شكراً لك على التسجيل في منصة نقل الشحنات!</p>
    <p><strong>مهم:</strong> يجب عليك التحقق من عنوان بريدك الإلكتروني قبل أن تتمكن من تسجيل الدخول إلى حسابك.</p>
    <p>يرجى استخدام كلمة المرور لمرة واحدة (OTP) التالية للتحقق من عنوان بريدك الإلكتروني:</p>
    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #4CAF50;">
        ${otp}
      </div>
    </div>
    <p><strong>ستنتهي صلاحية هذا الرمز خلال 5 دقائق.</strong></p>
    <p>أدخل هذا الرمز في نموذج التحقق لإكمال التحقق من بريدك الإلكتروني.</p>
    <p>بعد التحقق، ستتمكن من تسجيل الدخول واستخدام جميع ميزات المنصة.</p>
    <p>إذا لم تطلب هذا التحقق، يرجى تجاهل هذا البريد الإلكتروني.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic password reset email template
 * @param name User's name
 * @param resetLink Link to reset password
 * @returns HTML string
 */
export function passwordResetEmailTemplateArabic(name: string, resetLink: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">إعادة تعيين كلمة المرور</h2>
    <p>مرحباً ${name}،</p>
    <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بك. انقر على الزر أدناه للتحقق من طلبك:</p>
    <p style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        التحقق من طلب إعادة التعيين
      </a>
    </p>
    <p>إذا لم يعمل الزر، يمكنك أيضاً نسخ ولصق الرابط التالي في متصفحك:</p>
    <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">${resetLink}</p>
    <p><strong>ستنتهي صلاحية هذا الرابط خلال 5 دقائق.</strong></p>
    <p>بعد التحقق، ستتمكن من تعيين كلمة مرور جديدة.</p>
    <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني أو التواصل مع الدعم الفني إذا كان لديك مخاوف.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic password changed confirmation email template
 * @param name User's name
 * @returns HTML string
 */
export function passwordChangedEmailTemplateArabic(name: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تم تغيير كلمة المرور بنجاح</h2>
    <p>مرحباً ${name}،</p>
    <p>تم تغيير كلمة المرور الخاصة بك بنجاح.</p>
    <p>إذا لم تقم بهذا التغيير، يرجى التواصل مع فريق الدعم الفني فوراً.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic shipment created email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param pickupCode Pickup code
 * @returns HTML string
 */
export function shipmentCreatedEmailTemplateArabic(name: string, shipmentId: string, pickupCode: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تم إنشاء الشحنة بنجاح</h2>
    <p>مرحباً ${name}،</p>
    <p>تم إنشاء شحنتك بنجاح.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>رمز الاستلام:</strong> ${pickupCode}</p>
    </div>
    <p>يرجى الاحتفاظ برمز الاستلام بأمان حيث سيكون مطلوباً للمستلم لاستلام الطرد.</p>
    <p>يمكنك تتبع حالة شحنتك على منصتنا.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic shipment status update email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param status New status
 * @param details Optional details
 * @returns HTML string
 */
export function shipmentStatusUpdateEmailTemplateArabic(
  name: string,
  shipmentId: string,
  status: string,
  details?: string
): string {
  const statusArabic = getShipmentStatusInArabic(status);
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تحديث حالة الشحنة</h2>
    <p>مرحباً ${name}،</p>
    <p>تم تحديث حالة شحنتك.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>الحالة الجديدة:</strong> ${statusArabic}</p>
      ${details ? `<p><strong>التفاصيل:</strong> ${details}</p>` : ''}
    </div>
    <p>يمكنك تتبع شحنتك على منصتنا لمزيد من التفاصيل.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic new shipment notification for Access Operator template
 * @param businessName Business name
 * @param shipmentId Shipment ID
 * @returns HTML string
 */
export function newShipmentNotificationAOTemplateArabic(businessName: string, shipmentId: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تنبيه شحنة جديدة</h2>
    <p>مرحباً ${businessName}،</p>
    <p>تم إنشاء شحنة جديدة مع موقعك كنقطة استلام أو تسليم.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
    </div>
    <p>يرجى مراجعة لوحة التحكم الخاصة بك لمزيد من التفاصيل.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic available shipment notification for Car Operator template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param originLocation Origin location
 * @param destLocation Destination location
 * @returns HTML string
 */
export function availableShipmentCOTemplateArabic(
  name: string,
  shipmentId: string,
  originLocation: string,
  destLocation: string
): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">شحنة متاحة</h2>
    <p>مرحباً ${name}،</p>
    <p>شحنة جديدة متاحة للاستلام تتطابق مع مسارك:</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>المنشأ:</strong> ${originLocation}</p>
      <p><strong>الوجهة:</strong> ${destLocation}</p>
    </div>
    <p>يرجى مراجعة لوحة التحكم الخاصة بك لقبول هذه الشحنة.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Helper function to get user type in Arabic
 */
function getUserTypeInArabic(userType: string): string {
  switch (userType) {
    case 'CUSTOMER':
      return 'عميل';
    case 'ACCESS_OPERATOR':
      return 'مشغل نقطة وصول';
    case 'CAR_OPERATOR':
      return 'مشغل مركبة';
    case 'ADMIN':
      return 'مدير';
    default:
      return userType;
  }
}

/**
 * Arabic admin OTP verification email template
 * @param name Admin's name
 * @param otp One-time password
 * @returns HTML string
 */
export function adminOTPEmailTemplateArabic(name: string, otp: string): string {
  const content = `
    <h2 style="color: #FF9800; margin-bottom: 20px;">التحقق من حساب المدير</h2>
    <p>مرحباً ${name}،</p>
    <p>شكراً لك على التسجيل كمدير في منصة نقل الشحنات.</p>
    <p>للتحقق من حسابك، يرجى استخدام كلمة المرور لمرة واحدة (OTP) التالية:</p>
    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 5px;">
        ${otp}
      </div>
    </div>
    <p><strong>ستنتهي صلاحية هذا الرمز خلال 5 دقائق.</strong></p>
    <p>إذا لم تطلب هذا التحقق، يرجى تجاهل هذا البريد الإلكتروني أو التواصل مع مدير النظام.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic shipment expired notification email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @returns HTML string
 */
export function shipmentExpiredEmailTemplateArabic(name: string, shipmentId: string): string {
  const content = `
    <h2 style="color: #FF5722; margin-bottom: 20px;">تم إلغاء الشحنة تلقائياً</h2>
    <p>مرحباً ${name}،</p>
    <p>نأسف لإبلاغك أنه تم إلغاء شحنتك تلقائياً بسبب سياسة انتهاء الصلاحية خلال 24 ساعة.</p>
    <div style="background-color: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF9800;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>السبب:</strong> تجاوز الموعد النهائي للاستلام خلال 24 ساعة</p>
    </div>
    <p><strong>ماذا حدث؟</strong></p>
    <p>يجب تسليم جميع الشحنات إلى مشغل نقطة الوصول خلال 24 ساعة من الإنشاء. نظراً لعدم الوفاء بهذا الموعد النهائي، تم إلغاء الشحنة تلقائياً بواسطة نظامنا.</p>
    <p><strong>ماذا يمكنك فعله؟</strong></p>
    <ul>
      <li>إنشاء شحنة جديدة إذا كنت لا تزال بحاجة إلى إرسال طردك</li>
      <li>تأكد من التسليم إلى مشغل نقطة الوصول خلال 24 ساعة في المرة القادمة</li>
      <li>التواصل مع الدعم الفني إذا كان لديك أي أسئلة</li>
    </ul>
    <p>نعتذر عن أي إزعاج قد يسببه هذا.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic admin welcome email template
 * @param name Admin's name
 * @param role Admin's role
 * @returns HTML string
 */
export function adminWelcomeEmailTemplateArabic(name: string, role: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">مرحباً بك في بوابة إدارة منصة نقل الشحنات!</h2>
    <p>مرحباً ${name}،</p>
    <p>شكراً لك على الانضمام إلى فريقنا كـ <strong>${role}</strong>.</p>
    <p>لديك الآن إمكانية الوصول إلى بوابة الإدارة حيث يمكنك إدارة المستخدمين والشحنات وعمليات المنصة الأخرى.</p>
    <p>إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، يرجى التواصل مع مدير النظام.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic admin password reset email template
 * @param name Admin's name
 * @param resetLink Password reset link
 * @returns HTML string
 */
export function adminPasswordResetEmailTemplateArabic(name: string, resetLink: string): string {
  const content = `
    <h2 style="color: #FF9800; margin-bottom: 20px;">إعادة تعيين كلمة مرور المدير</h2>
    <p>مرحباً ${name}،</p>
    <p>لقد طلبت إعادة تعيين كلمة مرور حساب المدير الخاص بك.</p>
    <p>يرجى النقر على الزر أدناه لإعادة تعيين كلمة المرور. ستنتهي صلاحية هذا الرابط خلال 5 دقائق.</p>
    <div style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">إعادة تعيين كلمة المرور</a>
    </div>
    <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني أو التواصل مع مدير النظام.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Arabic admin password changed email template
 * @param name Admin's name
 * @returns HTML string
 */
export function adminPasswordChangedEmailTemplateArabic(name: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تم تغيير كلمة مرور المدير</h2>
    <p>مرحباً ${name}،</p>
    <p>تم تغيير كلمة مرور حساب المدير الخاص بك بنجاح.</p>
    <p>إذا لم تقم بهذا التغيير، يرجى التواصل مع مدير النظام فوراً.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;

  return baseArabicTemplate(content);
}

/**
 * Helper function to get shipment status in Arabic
 */
function getShipmentStatusInArabic(status: string): string {
  switch (status) {
    case 'PENDING':
      return 'في الانتظار';
    case 'AWAITING_PICKUP':
      return 'في انتظار الاستلام';
    case 'PICKED_UP_BY_CO':
      return 'تم الاستلام من قبل مشغل المركبة';
    case 'IN_TRANSIT':
      return 'في الطريق';
    case 'ARRIVED_AT_DESTINATION':
      return 'وصل إلى الوجهة';
    case 'READY_FOR_DELIVERY':
      return 'جاهز للتسليم';
    case 'DELIVERED':
      return 'تم التسليم';
    case 'CANCELLED':
      return 'ملغي';
    default:
      return status;
  }
}

/**
 * Modern shipment status update notification (Arabic content)
 * @param name Recipient's name
 * @param shipmentId Shipment ID
 * @param status Current status
 * @param trackingCode Tracking code
 * @param details Additional details
 * @returns Arabic content for modern email
 */
export function modernShipmentUpdateArabicContent(
  name: string,
  shipmentId: string,
  status: string,
  trackingCode: string,
  details?: any
): string {
  const statusArabic = getShipmentStatusInArabic(status);
  const currentDate = new Date().toLocaleDateString('ar-SA');

  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 24px; font-weight: 700; direction: rtl;" class="arabic-text">
        تحديث حالة الشحنة
      </h2>
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 20px; font-weight: 700;" class="english-text">
        Shipment Status Update
      </h2>
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        ${currentDate}
      </p>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          مرحباً ${name}،
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          تم تحديث حالة شحنتك. نحن نرسل لك هذا الإشعار عند تغيير حالة الشحنة.
        </p>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          Hello ${name},
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          Your shipment status has been updated. We're sending you this notification whenever your shipment status changes.
        </p>
      </div>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
          معلومات الشحنة المحدثة
        </h4>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              رقم الشحنة:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${shipmentId}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              رمز التتبع:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${trackingCode}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              الحالة الحالية:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
              <span style="background-color: #2196F3; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                ${statusArabic}
              </span>
            </td>
          </tr>
          ${details ? `
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #555;">
              تفاصيل إضافية:
            </td>
            <td style="padding: 8px 0; color: #333;">
              ${details}
            </td>
          </tr>
          ` : ''}
        </table>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
          Updated Shipment Information
        </h4>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              Shipment ID:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${shipmentId}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              Tracking Code:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${trackingCode}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              Current Status:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
              <span style="background-color: #2196F3; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase;">
                ${status.replace('_', ' ')}
              </span>
            </td>
          </tr>
          ${details ? `
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #555;">
              Additional Details:
            </td>
            <td style="padding: 8px 0; color: #333;">
              ${details}
            </td>
          </tr>
          ` : ''}
        </table>
      </div>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px; direction: rtl;" class="arabic-text">
        شكراً لاستخدامك نقلات، فريق نقلات
      </p>
      <p style="margin: 5px 0; color: #666; font-size: 14px;" class="english-text">
        Thank you for using NAQALAT, NAQALAT Team
      </p>
    </div>
  `;
}

/**
 * Modern shipment created notification (Arabic content)
 * @param name Customer's name
 * @param shipmentId Shipment ID
 * @param pickupCode Pickup code
 * @param trackingCode Tracking code
 * @returns Arabic content for modern email
 */
export function modernShipmentCreatedArabicContent(
  name: string,
  shipmentId: string,
  pickupCode: string,
  trackingCode: string
): string {
  const currentDate = new Date().toLocaleDateString('ar-SA');

  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 24px; font-weight: 700; direction: rtl;" class="arabic-text">
        تم إنشاء الشحنة بنجاح
      </h2>
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 20px; font-weight: 700;" class="english-text">
        Shipment Created Successfully
      </h2>
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        ${currentDate}
      </p>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          مرحباً ${name}،
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          تم إنشاء شحنتك بنجاح. سنرسل لك إشعارات عند تحديث حالة الشحنة.
        </p>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          Hello ${name},
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          Your shipment has been created successfully. We'll send you notifications when your shipment status is updated.
        </p>
      </div>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
          تفاصيل الشحنة
        </h4>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              رقم الشحنة:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${shipmentId}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              رمز التتبع:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${trackingCode}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #555;">
              رمز الاستلام:
            </td>
            <td style="padding: 8px 0;">
              <span style="background-color: #2196F3; color: white; padding: 6px 15px; border-radius: 25px; font-size: 14px; font-weight: 700; letter-spacing: 2px;">
                ${pickupCode}
              </span>
            </td>
          </tr>
        </table>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
          Shipment Details
        </h4>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              Shipment ID:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${shipmentId}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
              Tracking Code:
            </td>
            <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
              ${trackingCode}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #555;">
              Pickup Code:
            </td>
            <td style="padding: 8px 0;">
              <span style="background-color: #2196F3; color: white; padding: 6px 15px; border-radius: 25px; font-size: 14px; font-weight: 700; letter-spacing: 2px;">
                ${pickupCode}
              </span>
            </td>
          </tr>
        </table>
      </div>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 15px;" class="arabic-text">
        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
          <strong>مهم:</strong> احتفظ برمز الاستلام بأمان. سيحتاج المستلم إلى هذا الرمز لاستلام الطرد.
        </p>
      </div>
      <div style="direction: ltr; text-align: left;" class="english-text">
        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
          <strong>Important:</strong> Keep the pickup code safe. The recipient will need this code to collect the package.
        </p>
      </div>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px; direction: rtl;" class="arabic-text">
        شكراً لاستخدامك نقلات، فريق نقلات
      </p>
      <p style="margin: 5px 0; color: #666; font-size: 14px;" class="english-text">
        Thank you for using NAQALAT, NAQALAT Team
      </p>
    </div>
  `;
}

/**
 * Modern welcome email content (Combined Arabic & English) - for use with modern template
 */
export function modernWelcomeArabicContent(name: string, userType: string): string {
  const userTypeArabic = getUserTypeInArabic(userType);
  const userTypeEnglish = userType.replace('_', ' ').toLowerCase();

  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 24px; font-weight: 700; direction: rtl;" class="arabic-text">
        مرحباً بك في نقلات!
      </h2>
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 20px; font-weight: 700;" class="english-text">
        Welcome to NAQALAT!
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          مرحباً ${name}،
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          شكراً لك على التسجيل كـ ${userTypeArabic} في منصتنا.
        </p>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          نحن متحمسون لانضمامك إلينا ونتطلع إلى تقديم تجربة سلسة لك.
        </p>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، لا تتردد في التواصل مع فريق الدعم الفني.
        </p>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          Hello ${name},
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          Thank you for registering as a ${userTypeEnglish} on our platform.
        </p>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          We're excited to have you on board and look forward to providing you with a seamless experience.
        </p>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          If you have any questions or need assistance, please don't hesitate to contact our support team.
        </p>
      </div>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px; direction: rtl;" class="arabic-text">
        مع أطيب التحيات، فريق نقلات
      </p>
      <p style="margin: 5px 0; color: #666; font-size: 14px;" class="english-text">
        Best regards, NAQALAT Team
      </p>
    </div>
  `;
}

/**
 * Modern verification OTP email content (Combined Arabic & English) - for use with modern template
 */
export function modernVerificationOTPArabicContent(name: string, otp: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 10px 0; font-size: 24px; font-weight: 700; direction: rtl;" class="arabic-text">
        التحقق من البريد الإلكتروني مطلوب
      </h2>
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 20px; font-weight: 700;" class="english-text">
        Email Verification Required
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 20px;" class="arabic-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          مرحباً ${name}،
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          شكراً لك على التسجيل في نقلات!
        </p>
      </div>

      <div style="direction: ltr; text-align: left;" class="english-text">
        <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
          Hello ${name},
        </h3>
        <p style="margin: 10px 0; line-height: 1.6; color: #333;">
          Thank you for registering with NAQALAT!
        </p>
      </div>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <div style="direction: rtl; text-align: right; margin-bottom: 15px;" class="arabic-text">
        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
          <strong>مهم:</strong> يجب عليك التحقق من عنوان بريدك الإلكتروني قبل أن تتمكن من تسجيل الدخول إلى حسابك.
        </p>
      </div>
      <div style="direction: ltr; text-align: left;" class="english-text">
        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
          <strong>Important:</strong> You must verify your email address before you can log in to your account.
        </p>
      </div>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0; text-align: center;">
      <div style="direction: rtl; text-align: center; margin-bottom: 15px;" class="arabic-text">
        <p style="margin: 10px 0; color: #333;">
          يرجى استخدام كلمة المرور لمرة واحدة (OTP) التالية للتحقق من عنوان بريدك الإلكتروني:
        </p>
      </div>
      <div style="direction: ltr; text-align: center; margin-bottom: 20px;" class="english-text">
        <p style="margin: 10px 0; color: #333;">
          Please use the following One-Time Password (OTP) to verify your email address:
        </p>
      </div>

      <div style="margin: 25px 0;">
        <span style="background-color: #2196F3; color: white; padding: 15px 25px; border-radius: 25px; font-size: 24px; font-weight: 700; letter-spacing: 3px;">
          ${otp}
        </span>
      </div>

      <div style="direction: rtl; text-align: center; margin-bottom: 10px;" class="arabic-text">
        <p style="margin: 5px 0; color: #666; font-size: 12px;">
          <strong>ستنتهي صلاحية هذا الرمز خلال 5 دقائق.</strong>
        </p>
      </div>
      <div style="direction: ltr; text-align: center;" class="english-text">
        <p style="margin: 5px 0; color: #666; font-size: 12px;">
          <strong>This OTP will expire in 5 minutes.</strong>
        </p>
      </div>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px; direction: rtl;" class="arabic-text">
        مع أطيب التحيات، فريق نقلات
      </p>
      <p style="margin: 5px 0; color: #666; font-size: 14px;" class="english-text">
        Best regards, NAQALAT Team
      </p>
    </div>
  `;
}
