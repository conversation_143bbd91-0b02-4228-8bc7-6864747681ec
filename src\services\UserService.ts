import crypto from "crypto";

import { PrismaClient } from "@prisma/client";
import bcrypt from "bcrypt";

import { EmailService } from "./EmailService";
import { IUserService, IAuditLogService } from "../interfaces/services";
import { UserCreateSchema, UserUpdateSchema } from "../schemas";
import { BaseService } from "./BaseService";
import { User, UserStatus, UserType } from "../types/models";
import { accountActivatedEmailTemplate } from "../utils/emailTemplates";
import { AppError } from "../utils/errors";

export class UserService extends BaseService implements IUserService {
  private auditLogService: IAuditLogService;
  private emailService: EmailService;

  constructor(
    prisma: PrismaClient,
    auditLogService: IAuditLogService,
    emailService: EmailService
  ) {
    super(prisma);
    this.auditLogService = auditLogService;
    this.emailService = emailService;
  }

  async createUser(
    userData: Omit<User, "id" | "created_at" | "updated_at">
  ): Promise<User> {
    try {
      // Validate user data with Zod
      const validatedData = UserCreateSchema.parse({
        name: userData.name,
        email: userData.email,
        password: userData.password_hash, // Original password before hashing
        phone: userData.phone,
        user_type: userData.user_type,
      });

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: validatedData.email },
      });

      if (existingUser) {
        throw AppError.conflict("User with this email already exists");
      }

      // Hash the password
      const hashedPassword = await bcrypt.hash(validatedData.password, 10);

      // Create the user - ACTIVE by default, email verification still required
      const user = await this.prisma.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password_hash: hashedPassword,
          phone: validatedData.phone,
          user_type: validatedData.user_type,
          status: UserStatus.ACTIVE, // Changed: Users are ACTIVE by default
          email_verified: false,
        },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "USER_CREATED",
        undefined,
        {
          email: user.email,
          user_type: user.user_type,
        }
      );

      // Create and send verification OTP
      await this.sendVerificationOTP(user.email);

      return user as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error creating user:", error);
      throw AppError.internal("Failed to create user");
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      if (!id) {
        throw AppError.badRequest("User ID is required");
      }

      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        return null;
      }

      return user as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error getting user by ID:", error);
      throw AppError.internal("Failed to get user");
    }
  }

  /**
   * Get access operator data by user ID
   */
  async getAccessOperatorData(userId: string): Promise<any | null> {
    try {
      const accessOperator = await this.prisma.accessOperator.findUnique({
        where: { id: userId },
      });

      return accessOperator;
    } catch (error) {
      console.error("Error getting access operator data:", error);
      throw AppError.internal("Failed to get access operator data");
    }
  }

  /**
   * Get car operator data by user ID
   */
  async getCarOperatorData(userId: string): Promise<any | null> {
    try {
      const carOperator = await this.prisma.carOperator.findUnique({
        where: { id: userId },
      });

      return carOperator;
    } catch (error) {
      console.error("Error getting car operator data:", error);
      throw AppError.internal("Failed to get car operator data");
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    try {
      if (!email) {
        throw AppError.badRequest("Email is required");
      }

      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        return null;
      }

      return user as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error getting user by email:", error);
      throw AppError.internal("Failed to get user");
    }
  }

  async updateUser(
    id: string,
    userData: Partial<User>,
    accessOperatorData?: any,
    carOperatorData?: any,
    updatedBy?: string
  ): Promise<User> {
    try {
      // Validate user exists
      const existingUser = await this.getUserById(id);
      if (!existingUser) {
        throw AppError.notFound(`User with ID ${id} not found`);
      }

      // Validate update data with Zod
      const validatedData = UserUpdateSchema.parse({
        name: userData.name,
        phone: userData.phone,
      });

      // Additional validation for required fields when provided
      if (
        userData.name !== undefined &&
        (!userData.name || userData.name.trim().length < 2)
      ) {
        throw AppError.validation("Validation error", {
          name: ["Name must be at least 2 characters long"],
        });
      }
      if (
        userData.phone !== undefined &&
        (!userData.phone || userData.phone.length < 10)
      ) {
        throw AppError.validation("Validation error", {
          phone: ["Phone number must be at least 10 characters long"],
        });
      }

      // Prepare data for update
      const updateData: any = {};
      if (validatedData.name !== undefined)
        updateData.name = validatedData.name;
      if (validatedData.phone !== undefined)
        updateData.phone = validatedData.phone;

      // Check if there's any data to update
      if (
        Object.keys(updateData).length === 0 &&
        !accessOperatorData &&
        !carOperatorData
      ) {
        throw AppError.badRequest("No data provided for update");
      }

      // Password updates are not allowed through this endpoint
      // Users should use the dedicated changePassword endpoint instead

      // Update the user with updatedBy tracking (only if there's user data to update)
      let user = existingUser;
      if (Object.keys(updateData).length > 0) {
        const finalUpdateData = this.addUpdatedBy(updateData, updatedBy);

        user = (await this.prisma.user.update({
          where: { id },
          data: finalUpdateData,
        })) as unknown as User;
      }

      // Update user type-specific data if provided
      if (
        existingUser.user_type === UserType.ACCESS_OPERATOR &&
        accessOperatorData
      ) {
        // Update access operator data
        await this.updateAccessOperatorData(id, accessOperatorData, updatedBy);
      } else if (
        existingUser.user_type === UserType.CAR_OPERATOR &&
        carOperatorData
      ) {
        // Update car operator data
        await this.updateCarOperatorData(id, carOperatorData, updatedBy);
      }

      // Log the action
      await this.auditLogService.createAuditLog(id, "USER_UPDATED", undefined, {
        id,
        updated_fields: Object.keys(updateData),
      });

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error updating user:", error);
      throw AppError.internal("Failed to update user");
    }
  }

  /**
   * Update access operator data (creates record if it doesn't exist)
   */
  async updateAccessOperatorData(
    userId: string,
    data: any,
    updatedBy?: string
  ): Promise<any> {
    try {
      // Validate data
      if (!data) {
        throw AppError.badRequest("Access operator data is required");
      }

      // Prepare update data
      const updateData: any = {};
      if (data.business_name !== undefined)
        updateData.business_name = data.business_name;
      if (data.address !== undefined) updateData.address = data.address;
      if (data.geo_latitude !== undefined)
        updateData.geo_latitude = data.geo_latitude;
      if (data.geo_longitude !== undefined)
        updateData.geo_longitude = data.geo_longitude;

      // Use upsert to create or update access operator data
      const accessOperator = await this.prisma.accessOperator.upsert({
        where: { id: userId },
        update: this.addUpdatedBy(updateData, updatedBy),
        create: {
          id: userId,
          business_name: data.business_name || "",
          address: data.address || "",
          geo_latitude: data.geo_latitude || null,
          geo_longitude: data.geo_longitude || null,
          approved: false, // Require admin approval for new access operators
        },
      });

      return accessOperator;
    } catch (error) {
      console.error("Error updating access operator data:", error);
      throw AppError.internal("Failed to update access operator data");
    }
  }

  /**
   * Update car operator data (creates record if it doesn't exist)
   */
  async updateCarOperatorData(
    userId: string,
    data: any,
    updatedBy?: string
  ): Promise<any> {
    try {
      // Validate data
      if (!data) {
        throw AppError.badRequest("Car operator data is required");
      }

      // Prepare update data
      const updateData: any = {};
      if (data.license_number !== undefined)
        updateData.license_number = data.license_number;
      if (data.vehicle_info !== undefined)
        updateData.vehicle_info = data.vehicle_info;
      if (data.pickup_access_point_id !== undefined)
        updateData.pickup_access_point_id = data.pickup_access_point_id;
      if (data.dropoff_access_point_id !== undefined)
        updateData.dropoff_access_point_id = data.dropoff_access_point_id;

      // Validate access points exist (relaxed validation during development)
      if (data.pickup_access_point_id) {
        const pickupAO = await this.prisma.accessOperator.findFirst({
          where: {
            id: data.pickup_access_point_id,
            // Removed strict approval and status checks for development
            // approved: true,
            // user: { status: "ACTIVE" },
          },
        });
        if (!pickupAO) {
          throw AppError.badRequest(
            "Invalid pickup access point - access point does not exist"
          );
        }
        console.info(
          `Pickup access point validation passed for: ${data.pickup_access_point_id}`
        );
      }

      if (data.dropoff_access_point_id) {
        const dropoffAO = await this.prisma.accessOperator.findFirst({
          where: {
            id: data.dropoff_access_point_id,
            // Removed strict approval and status checks for development
            // approved: true,
            // user: { status: "ACTIVE" },
          },
        });
        if (!dropoffAO) {
          throw AppError.badRequest(
            "Invalid dropoff access point - access point does not exist"
          );
        }
        console.info(
          `Dropoff access point validation passed for: ${data.dropoff_access_point_id}`
        );
      }

      // Use upsert to create or update car operator data
      const carOperator = await this.prisma.carOperator.upsert({
        where: { id: userId },
        update: this.addUpdatedBy(updateData, updatedBy),
        create: {
          id: userId,
          license_number: data.license_number || "",
          vehicle_info: data.vehicle_info || "",
          pickup_access_point_id: data.pickup_access_point_id || null,
          dropoff_access_point_id: data.dropoff_access_point_id || null,
          approved: false, // Require admin approval for new car operators
        },
      });

      console.info(
        `Car operator profile ${carOperator.id} updated/created with approval status: ${carOperator.approved}`
      );

      return carOperator;
    } catch (error) {
      console.error("Error updating car operator data:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw AppError.internal("Failed to update car operator data");
    }
  }

  async changeUserStatus(
    id: string,
    status: string,
    updatedBy?: string
  ): Promise<User> {
    try {
      // Validate user exists
      const existingUser = await this.getUserById(id);
      if (!existingUser) {
        throw AppError.notFound(`User with ID ${id} not found`);
      }

      // Validate status
      const validStatus = Object.values(UserStatus).includes(
        status as UserStatus
      );
      if (!validStatus) {
        throw AppError.badRequest(`Invalid status: ${status}`);
      }

      // Update user status
      const user = await this.prisma.user.update({
        where: { id },
        data: this.addUpdatedBy({ status: status as UserStatus }, updatedBy),
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        id,
        "USER_STATUS_CHANGED",
        undefined,
        {
          id,
          old_status: existingUser.status,
          new_status: status,
        }
      );

      // Send email notification if status changed to ACTIVE
      if (
        status === UserStatus.ACTIVE &&
        existingUser.status !== UserStatus.ACTIVE
      ) {
        await this.emailService.sendEmail({
          to: user.email,
          subject: "Account Activated - Shipment Relay Platform",
          html: accountActivatedEmailTemplate(user.name),
        });
      }

      return user as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error changing user status:", error);
      throw AppError.internal("Failed to change user status");
    }
  }

  async validateCredentials(
    email: string,
    password: string
  ): Promise<User | null> {
    try {
      if (!email || !password) {
        throw AppError.badRequest("Email and password are required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        return null;
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(
        password,
        user.password_hash
      );
      if (!isPasswordValid) {
        return null;
      }

      // Check if email is verified
      if (!user.email_verified) {
        throw AppError.forbidden(
          "Email not verified. Please check your email for verification link."
        );
      }

      // Log the successful login
      await this.auditLogService.createAuditLog(
        user.id,
        "USER_LOGIN",
        undefined,
        {
          email: user.email,
        }
      );

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error validating credentials:", error);
      throw AppError.internal("Failed to validate credentials");
    }
  }

  /**
   * Verify user email with OTP
   */
  async verifyEmailWithOTP(otp: string): Promise<User> {
    try {
      // Validate OTP
      if (!otp) {
        throw AppError.badRequest("OTP is required");
      }

      // Find the verification record (must be unused and not expired)
      const verification = await this.prisma.userVerification.findFirst({
        where: {
          otp,
          used: false,
          expires_at: {
            gt: new Date(),
          },
        },
        include: {
          user: true,
        },
      });

      if (!verification) {
        throw AppError.badRequest("Invalid or expired OTP");
      }

      const user = verification.user;

      // Check if email is already verified
      if (user.email_verified) {
        throw AppError.conflict("Email is already verified");
      }

      // Mark the verification OTP as used
      await this.prisma.userVerification.update({
        where: { id: verification.id },
        data: { used: true },
      });

      // Update user record and auto-create operator profiles
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update user status
        const user_updated = await tx.user.update({
          where: { id: user.id },
          data: {
            email_verified: true,
            status: UserStatus.ACTIVE,
          },
        });

        // Auto-create operator profiles based on user type (approval depends on type)
        if (user.user_type === UserType.ACCESS_OPERATOR) {
          // Create AccessOperator profile - PENDING admin approval
          await tx.accessOperator.upsert({
            where: { id: user.id },
            update: { approved: false }, // Reset to pending if already exists
            create: {
              id: user.id,
              business_name: "", // Will be filled when user updates profile
              address: "",
              geo_latitude: null,
              geo_longitude: null,
              approved: false, // Requires admin approval
            },
          });
          console.info(
            `Auto-created AccessOperator profile for user ${user.id} - pending admin approval`
          );
        } else if (user.user_type === UserType.CAR_OPERATOR) {
          // Create CarOperator profile - PENDING admin approval
          await tx.carOperator.upsert({
            where: { id: user.id },
            update: { approved: false }, // Reset to pending if already exists
            create: {
              id: user.id,
              license_number: "", // Will be filled when user updates profile
              vehicle_info: "",
              pickup_access_point_id: null,
              dropoff_access_point_id: null,
              approved: false, // Requires admin approval
            },
          });
          console.info(
            `Auto-created CarOperator profile for user ${user.id} - pending admin approval`
          );
        }
        // Note: Customers don't need approval - they can use the system immediately

        return user_updated;
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "EMAIL_VERIFIED",
        undefined,
        {
          email: user.email,
        }
      );

      // Send welcome email
      await this.emailService.sendWelcomeEmail(
        user.email,
        user.name,
        user.user_type
      );

      return updatedUser as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error verifying email with OTP:", error);
      throw AppError.internal("Failed to verify email");
    }
  }

  /**
   * Legacy method for backward compatibility - now redirects to OTP verification
   */
  async verifyEmail(email: string, token: string): Promise<User> {
    try {
      // Validate email and token
      if (!email || !token) {
        throw AppError.badRequest("Email and token are required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Check if email is already verified
      if (user.email_verified) {
        throw AppError.conflict("Email is already verified");
      }

      // Find the verification record
      const verification = await this.prisma.emailVerification.findFirst({
        where: {
          user_id: user.id,
          token,
          used: false,
          expires_at: {
            gt: new Date(), // Token must not be expired
          },
        },
      });

      if (!verification) {
        throw AppError.badRequest("Invalid or expired verification token");
      }

      // Mark the verification token as used
      await this.prisma.emailVerification.update({
        where: { id: verification.id },
        data: { used: true },
      });

      // Update user and auto-create operator profiles
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update user status
        const user_updated = await tx.user.update({
          where: { id: user.id },
          data: {
            email_verified: true,
            status: UserStatus.ACTIVE,
          },
        });

        // Auto-create operator profiles based on user type (approval depends on type)
        if (user.user_type === UserType.ACCESS_OPERATOR) {
          // Create AccessOperator profile - PENDING admin approval
          await tx.accessOperator.upsert({
            where: { id: user.id },
            update: { approved: false }, // Reset to pending if already exists
            create: {
              id: user.id,
              business_name: "", // Will be filled when user updates profile
              address: "",
              geo_latitude: null,
              geo_longitude: null,
              approved: false, // Requires admin approval
            },
          });
          console.info(
            `Auto-created AccessOperator profile for user ${user.id} - pending admin approval`
          );
        } else if (user.user_type === UserType.CAR_OPERATOR) {
          // Create CarOperator profile - PENDING admin approval
          await tx.carOperator.upsert({
            where: { id: user.id },
            update: { approved: false }, // Reset to pending if already exists
            create: {
              id: user.id,
              license_number: "", // Will be filled when user updates profile
              vehicle_info: "",
              pickup_access_point_id: null,
              dropoff_access_point_id: null,
              approved: false, // Requires admin approval
            },
          });
          console.info(
            `Auto-created CarOperator profile for user ${user.id} - pending admin approval`
          );
        }
        // Note: Customers don't need approval - they can use the system immediately

        return user_updated;
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "EMAIL_VERIFIED",
        undefined,
        {
          email: user.email,
        }
      );

      // Send welcome email
      await this.emailService.sendWelcomeEmail(
        user.email,
        user.name,
        user.user_type
      );

      return updatedUser as User;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error verifying email:", error);
      throw AppError.internal("Failed to verify email");
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      // Validate email
      if (!email) {
        throw AppError.badRequest("Email is required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString("hex");

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create password reset record
      await this.prisma.passwordReset.create({
        data: {
          user_id: user.id,
          token: resetToken,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Generate reset link - use server URL directly
      const serverUrl = process.env.SERVER_URL || "http://localhost:8000";
      const resetLink = `${serverUrl}/api/auth/verify-reset-token?token=${resetToken}&email=${encodeURIComponent(
        email
      )}`;

      // Send password reset email
      await this.emailService.sendPasswordResetEmail(
        user.email,
        user.name,
        resetLink
      );

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "PASSWORD_RESET_REQUESTED",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error sending password reset email:", error);
      throw AppError.internal("Failed to send password reset email");
    }
  }

  /**
   * Verify password reset token
   */
  async verifyPasswordResetToken(email: string, token: string): Promise<void> {
    try {
      // Validate inputs
      if (!email || !token) {
        throw AppError.badRequest("Email and token are required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Find the password reset record
      const passwordReset = await this.prisma.passwordReset.findFirst({
        where: {
          user_id: user.id,
          token,
          used: false,
          expires_at: {
            gt: new Date(), // Token must not be expired
          },
        },
      });

      if (!passwordReset) {
        throw AppError.badRequest("Invalid or expired reset token");
      }

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "PASSWORD_RESET_TOKEN_VERIFIED",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error verifying password reset token:", error);
      throw AppError.internal("Failed to verify password reset token");
    }
  }

  /**
   * Reset user password
   */
  async resetPassword(
    email: string,
    token: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Validate inputs
      if (!email || !token || !newPassword) {
        throw AppError.badRequest(
          "Email, token, and new password are required"
        );
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Find the password reset record
      const passwordReset = await this.prisma.passwordReset.findFirst({
        where: {
          user_id: user.id,
          token,
          used: false,
          expires_at: {
            gt: new Date(), // Token must not be expired
          },
        },
      });

      if (!passwordReset) {
        throw AppError.badRequest("Invalid or expired reset token");
      }

      // Mark the reset token as used
      await this.prisma.passwordReset.update({
        where: { id: passwordReset.id },
        data: { used: true },
      });

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          password_hash: hashedPassword,
        },
      });

      // Send password changed confirmation email
      await this.emailService.sendPasswordChangedEmail(user.email, user.name);

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "PASSWORD_RESET_COMPLETED",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error resetting password:", error);
      throw AppError.internal("Failed to reset password");
    }
  }

  /**
   * Change user password (for authenticated users)
   */
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Validate inputs
      if (!userId || !currentPassword || !newPassword) {
        throw AppError.badRequest(
          "User ID, current password, and new password are required"
        );
      }

      // Get user by ID
      const user = await this.getUserById(userId);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(
        currentPassword,
        user.password_hash
      );
      if (!isPasswordValid) {
        throw AppError.badRequest("Current password is incorrect");
      }

      // Check if new password is the same as current password
      if (currentPassword === newPassword) {
        throw AppError.badRequest(
          "New password must be different from current password"
        );
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          password_hash: hashedPassword,
        },
      });

      // Send password changed confirmation email
      await this.emailService.sendPasswordChangedEmail(user.email, user.name);

      // Log the action
      await this.auditLogService.createAuditLog(
        userId,
        "PASSWORD_CHANGED",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error changing password:", error);
      throw AppError.internal("Failed to change password");
    }
  }

  /**
   * Get all users (admin only) with pagination, filtering, and searching
   * @param options Pagination, filtering, and searching options
   * @returns Object containing users array and total count
   */
  async getAllUsers(options?: {
    skip?: number;
    take?: number;
    search?: string;
    status?: string;
    user_type?: string;
    approval_status?: string;
  }): Promise<{ users: User[]; total: number }> {
    try {
      const { skip = 0, take = 10, search, status, user_type, approval_status } = options || {};

      // Build where conditions
      const where: any = {};

      // Add search condition if provided
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { email: { contains: search, mode: "insensitive" } },
          { phone: { contains: search } },
        ];
      }

      // Add status filter if provided
      if (status) {
        where.status = status;
      }

      // Add user_type filter if provided
      if (user_type) {
        where.user_type = user_type;
      }

      // Add approval_status filter if provided (for operators)
      if (approval_status) {
        if (approval_status === 'APPROVED') {
          where.OR = [
            {
              user_type: 'ACCESS_OPERATOR',
              accessOperator: { approved: true }
            },
            {
              user_type: 'CAR_OPERATOR',
              carOperator: { approved: true }
            },
            {
              user_type: 'CUSTOMER' // Customers don't need approval
            }
          ];
        } else if (approval_status === 'REJECTED' || approval_status === 'PENDING') {
          where.OR = [
            {
              user_type: 'ACCESS_OPERATOR',
              accessOperator: { approved: false }
            },
            {
              user_type: 'CAR_OPERATOR',
              carOperator: { approved: false }
            }
          ];
        }
      }

      // Get total count for pagination (count doesn't support include)
      const total = await this.prisma.user.count({ where });

      // Get users with pagination
      const users = await this.prisma.user.findMany({
        where,
        include: {
          accessOperator: true,
          carOperator: true,
        },
        orderBy: {
          created_at: "desc",
        },
        skip,
        take,
      });

      return { users: users as User[], total };
    } catch (error) {
      console.error("Error getting all users:", error);
      throw AppError.internal("Failed to get users");
    }
  }

  /**
   * Update user status (admin only)
   * @param userId User ID
   * @param status New status (ACTIVE/INACTIVE)
   * @returns Updated user
   */
  async updateUserStatus(userId: string, status: string): Promise<User> {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId }
      });

      if (!existingUser) {
        throw AppError.notFound("User not found");
      }

      // Map status to Prisma enum values
      let prismaStatus: 'ACTIVE' | 'PENDING' | 'SUSPENDED';

      switch (status.toUpperCase()) {
        case 'ACTIVE':
          prismaStatus = 'ACTIVE';
          break;
        case 'INACTIVE':
        case 'SUSPENDED':
          prismaStatus = 'SUSPENDED';
          break;
        case 'PENDING':
          prismaStatus = 'PENDING';
          break;
        default:
          throw AppError.badRequest(`Invalid status: ${status}. Must be ACTIVE, INACTIVE, SUSPENDED, or PENDING`);
      }

      console.log(`Updating user ${userId} status from ${existingUser.status} to ${prismaStatus}`);

      // Update user status
      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: {
          status: prismaStatus
        }
      });

      console.log(`User ${userId} status updated successfully to ${updatedUser.status}`);

      return updatedUser as User;
    } catch (error) {
      console.error("Error updating user status:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw AppError.internal("Failed to update user status");
    }
  }

  /**
   * Update operator approval status (admin only)
   * @param userId User ID
   * @param approved Approval status
   * @returns Updated operator info
   */
  async updateOperatorApproval(userId: string, approved: boolean): Promise<any> {
    try {
      // Get user with operator info
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          accessOperator: true,
          carOperator: true,
        }
      });

      if (!user) {
        throw AppError.notFound("User not found");
      }

      if (user.user_type === 'ACCESS_OPERATOR' && user.accessOperator) {
        console.log(`Updating Access Operator ${userId} approval from ${user.accessOperator.approved} to ${approved}`);

        // Update access operator approval
        const updatedOperator = await this.prisma.accessOperator.update({
          where: { id: userId },
          data: { approved }
        });

        console.log(`Access Operator ${userId} approval updated successfully to ${updatedOperator.approved}`);

        return {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            user_type: user.user_type,
          },
          operator: updatedOperator,
          approved: updatedOperator.approved
        };
      } else if (user.user_type === 'CAR_OPERATOR' && user.carOperator) {
        console.log(`Updating Car Operator ${userId} approval from ${user.carOperator.approved} to ${approved}`);

        // Update car operator approval
        const updatedOperator = await this.prisma.carOperator.update({
          where: { id: userId },
          data: { approved }
        });

        console.log(`Car Operator ${userId} approval updated successfully to ${updatedOperator.approved}`);

        return {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            user_type: user.user_type,
          },
          operator: updatedOperator,
          approved: updatedOperator.approved
        };
      } else {
        throw AppError.badRequest("User is not an operator or operator record not found");
      }
    } catch (error) {
      console.error("Error updating operator approval:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw AppError.internal("Failed to update operator approval");
    }
  }

  /**
   * Send verification OTP to user
   */
  async sendVerificationOTP(email: string): Promise<void> {
    try {
      // Validate email
      if (!email) {
        throw AppError.badRequest("Email is required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Check if email is already verified
      if (user.email_verified) {
        return; // Already verified, no need to send OTP
      }

      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create user verification record
      await this.prisma.userVerification.create({
        data: {
          user_id: user.id,
          otp,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Send verification OTP email
      await this.emailService.sendVerificationOTP(user.email, user.name, otp);

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "VERIFICATION_OTP_SENT",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error sending verification OTP:", error);
      throw AppError.internal("Failed to send verification OTP");
    }
  }

  /**
   * Resend verification OTP to user
   */
  async resendVerificationOTP(email: string): Promise<void> {
    try {
      // Validate email
      if (!email) {
        throw AppError.badRequest("Email is required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Check if email is already verified
      if (user.email_verified) {
        throw AppError.conflict("Email is already verified");
      }

      // Mark any existing unused OTPs as used
      await this.prisma.userVerification.updateMany({
        where: {
          user_id: user.id,
          used: false,
        },
        data: {
          used: true,
        },
      });

      // Generate new 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create new user verification record
      await this.prisma.userVerification.create({
        data: {
          user_id: user.id,
          otp,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Send verification OTP email
      await this.emailService.sendVerificationOTP(user.email, user.name, otp);

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "VERIFICATION_OTP_RESENT",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error resending verification OTP:", error);
      throw AppError.internal("Failed to resend verification OTP");
    }
  }

  /**
   * Legacy method - Send verification email to an existing user
   */
  async sendVerificationEmail(email: string): Promise<void> {
    try {
      // Validate email
      if (!email) {
        throw AppError.badRequest("Email is required");
      }

      // Get user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Check if email is already verified
      if (user.email_verified) {
        return; // Already verified, no need to send email
      }

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString("hex");

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create email verification record
      await this.prisma.emailVerification.create({
        data: {
          user_id: user.id,
          token: verificationToken,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Generate verification link - use server URL directly
      const serverUrl = process.env.SERVER_URL || "http://localhost:8000";
      const verificationLink = `${serverUrl}/api/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(
        user.email
      )}`;

      // Send verification email
      await this.emailService.sendVerificationEmail(
        user.email,
        user.name,
        verificationLink
      );

      // Log the action
      await this.auditLogService.createAuditLog(
        user.id,
        "VERIFICATION_EMAIL_SENT",
        undefined,
        {
          email: user.email,
        }
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error sending verification email:", error);
      throw AppError.internal("Failed to send verification email");
    }
  }

  /**
   * Logout user
   * This method logs the logout event and can be extended to invalidate tokens if needed
   */
  async logout(userId: string): Promise<void> {
    try {
      // Validate user ID
      if (!userId) {
        throw AppError.badRequest("User ID is required");
      }

      // Check if user exists
      const user = await this.getUserById(userId);
      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Log the logout action
      await this.auditLogService.createAuditLog(
        userId,
        "USER_LOGOUT",
        undefined,
        {
          email: user.email,
        }
      );

      // In a more advanced implementation, we could add the token to a blacklist
      // or invalidate all sessions for this user
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error logging out user:", error);
      throw AppError.internal("Failed to logout user");
    }
  }

  /**
   * Update access operator approval status
   */
  async updateAccessOperatorApproval(
    userId: string,
    approved: boolean
  ): Promise<any> {
    try {
      // Check if access operator exists
      const existingOperator = await this.prisma.accessOperator.findUnique({
        where: { id: userId },
      });

      if (!existingOperator) {
        throw AppError.notFound("Access operator not found");
      }

      // Update approval status
      const updatedOperator = await this.prisma.accessOperator.update({
        where: { id: userId },
        data: { approved },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        userId,
        "ACCESS_OPERATOR_APPROVAL_CHANGED",
        undefined,
        {
          id: userId,
          old_approved: existingOperator.approved,
          new_approved: approved,
        }
      );

      return updatedOperator;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error updating access operator approval:", error);
      throw AppError.internal("Failed to update access operator approval");
    }
  }

  /**
   * Update car operator approval status
   */
  async updateCarOperatorApproval(
    userId: string,
    approved: boolean
  ): Promise<any> {
    try {
      // Check if car operator exists
      const existingOperator = await this.prisma.carOperator.findUnique({
        where: { id: userId },
      });

      if (!existingOperator) {
        throw AppError.notFound("Car operator not found");
      }

      // Update approval status
      const updatedOperator = await this.prisma.carOperator.update({
        where: { id: userId },
        data: { approved },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        userId,
        "CAR_OPERATOR_APPROVAL_CHANGED",
        undefined,
        {
          id: userId,
          old_approved: existingOperator.approved,
          new_approved: approved,
        }
      );

      return updatedOperator;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error updating car operator approval:", error);
      throw AppError.internal("Failed to update car operator approval");
    }
  }

  /**
   * Auto-approve all existing unapproved operators (development utility)
   */
  async autoApproveAllOperators(): Promise<{
    accessOperators: number;
    carOperators: number;
  }> {
    try {
      console.info("Starting auto-approval of all existing operators...");

      const result = await this.prisma.$transaction(async (tx) => {
        // Auto-approve all unapproved access operators
        const accessOperatorUpdate = await tx.accessOperator.updateMany({
          where: { approved: false },
          data: { approved: true },
        });

        // Auto-approve all unapproved car operators
        const carOperatorUpdate = await tx.carOperator.updateMany({
          where: { approved: false },
          data: { approved: true },
        });

        return {
          accessOperators: accessOperatorUpdate.count,
          carOperators: carOperatorUpdate.count,
        };
      });

      console.info(
        `Auto-approved ${result.accessOperators} access operators and ${result.carOperators} car operators`
      );
      return result;
    } catch (error) {
      console.error("Error auto-approving operators:", error);
      throw AppError.internal("Failed to auto-approve operators");
    }
  }
}
