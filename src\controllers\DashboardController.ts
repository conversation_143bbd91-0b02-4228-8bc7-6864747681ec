import { Request, Response, NextFunction } from 'express';

import { DashboardService } from '../services/DashboardService';
import { ServiceFactory } from '../services/ServiceFactory';
import { UserType } from '../types/models';
import { AppError } from '../utils/errors';

export class DashboardController {
  /**
   * Get dashboard data for the authenticated user
   */
  static async getDashboard(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Get user information from the authenticated request
      const userId = req.user?.id;
      const userType = req.user?.user_type;
      const userEmail = req.user?.email;

      if (!userId || !userType || !userEmail) {
        throw AppError.unauthorized('Invalid token payload');
      }

      // Get user details from database
      const userService = ServiceFactory.getUserService();
      const user = await userService.getUserById(userId);

      if (!user) {
        throw AppError.notFound('User not found');
      }

      // Get dashboard service
      const prisma = ServiceFactory.getPrismaClient();
      const dashboardService = new DashboardService(prisma);

      // Get dashboard data based on user type
      const dashboardData = await dashboardService.getDashboardData(userId, userType as UserType);

      // Prepare response
      const response = {
        user_info: {
          id: user.id,
          name: user.name,
          email: user.email,
          user_type: user.user_type,
          status: user.status,
          email_verified: user.email_verified,
        },
        dashboard_data: dashboardData,
      };

      res.status(200).json({
        success: true,
        message: 'Dashboard data retrieved successfully',
        data: response,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get dashboard summary for quick overview
   */
  static async getDashboardSummary(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Get user information from the authenticated request
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      if (!userId || !userType) {
        throw AppError.unauthorized('Invalid token payload');
      }

      // Get dashboard service
      const prisma = ServiceFactory.getPrismaClient();
      const dashboardService = new DashboardService(prisma);

      // Get dashboard data
      const dashboardData = await dashboardService.getDashboardData(userId, userType as UserType);

      // Extract summary based on user type
      let summary: Record<string, any> = {};

      switch (userType) {
        case UserType.CUSTOMER:
          summary = {
            total_shipments: dashboardData.shipment_stats?.total || 0,
            active_shipments: (dashboardData.shipment_stats?.pending || 0) + 
                             (dashboardData.shipment_stats?.awaiting_pickup || 0) + 
                             (dashboardData.shipment_stats?.in_transit || 0) + 
                             (dashboardData.shipment_stats?.arrived_at_destination || 0),
            delivered_shipments: dashboardData.shipment_stats?.delivered || 0,
          };
          break;

        case UserType.ACCESS_OPERATOR:
          summary = {
            total_shipments: dashboardData.shipment_stats?.total || 0,
            pending_shipments: dashboardData.shipment_stats?.pending || 0,
            unused_qr_labels: dashboardData.qr_label_stats?.unused || 0,
            business_approved: dashboardData.business_info?.approved || false,
          };
          break;

        case UserType.CAR_OPERATOR:
          summary = {
            assigned_shipments: dashboardData.shipment_stats?.assigned || 0,
            completed_shipments: dashboardData.shipment_stats?.completed || 0,
            completion_rate: dashboardData.shipment_stats?.completion_rate || 0,
            operator_approved: dashboardData.operator_info?.approved || false,
          };
          break;

        case UserType.ADMIN:
          summary = {
            total_users: dashboardData.user_stats?.total || 0,
            total_shipments: dashboardData.shipment_stats?.total || 0,
            pending_approvals: dashboardData.user_stats?.pending_approvals || 0,
            delivery_rate: dashboardData.shipment_stats?.delivery_rate || 0,
          };
          break;

        default:
          throw AppError.badRequest('Invalid user type');
      }

      res.status(200).json({
        success: true,
        message: 'Dashboard summary retrieved successfully',
        data: {
          user_type: userType,
          summary,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
