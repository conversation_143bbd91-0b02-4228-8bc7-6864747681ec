/* eslint-disable no-console */
import { PrismaClient } from "@prisma/client";

import { EmailService } from "./EmailService";
import {
  NotificationService,
  CreateNotificationData,
} from "./NotificationService";
import {
  NotificationType,
  NotificationPriority,
  UserType,
} from "../types/models";

export interface ShipmentNotificationContext {
  shipment: any;
  customer?: any;
  originAO?: any;
  destAO?: any;
  carOperator?: any;
  actionUserId?: string;
  actionUserType?: UserType;
  metadata?: Record<string, any>;
}

/**
 * Service for managing shipment-related notifications
 * Handles complex notification logic for all shipment operations
 */
export class ShipmentNotificationService {
  private prisma: PrismaClient;
  private notificationService: NotificationService;
  private emailService: EmailService;

  constructor(
    prisma: PrismaClient,
    notificationService: NotificationService,
    emailService: EmailService
  ) {
    this.prisma = prisma;
    this.notificationService = notificationService;
    this.emailService = emailService;
  }

  /**
   * Send notifications when QR code is assigned to shipment
   */
  async notifyQRAssigned(context: ShipmentNotificationContext): Promise<void> {
    try {
      console.log(
        `📧 Sending QR assignment notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - QR assigned, package accepted
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.QR_CODE_ASSIGNED,
        title: "🏷️ QR Code Assigned",
        message: `Your package has been accepted at ${context.originAO?.business_name}. QR code assigned and package is now in the system.`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          origin_ao_name: context.originAO?.business_name,
          qr_assigned_at: new Date().toISOString(),
        },
      });

      // 2. Notify Origin AO - Package received confirmation
      if (context.originAO) {
        notifications.push({
          userId: context.originAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.QR_CODE_ASSIGNED,
          title: "📦 Package Received & QR Assigned",
          message: `Package from ${context.customer?.name} has been received and QR code assigned. Ready for car operator pickup.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            customer_name: context.customer?.name,
            tracking_code: context.shipment.tracking_code,
            qr_assigned_at: new Date().toISOString(),
          },
        });
      }

      // 3. Notify Destination AO - Package in system
      if (context.destAO && context.destAO.id !== context.originAO?.id) {
        notifications.push({
          userId: context.destAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.QR_CODE_ASSIGNED,
          title: "📦 Package in System",
          message: `Package for ${context.shipment.receiver_name} is now in the system and will be transported to your location.`,
          priority: NotificationPriority.LOW,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            tracking_code: context.shipment.tracking_code,
            origin_ao_name: context.originAO?.business_name,
          },
        });
      }

      // 4. Notify eligible Car Operators - Package ready for pickup
      const eligibleCarOperators = await this.getEligibleCarOperators(
        context.originAO?.id,
        context.destAO?.id
      );

      for (const carOperator of eligibleCarOperators) {
        notifications.push({
          userId: carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.PACKAGE_READY_FOR_PICKUP,
          title: "📦 Package Ready for Pickup",
          message: `Package is ready for pickup at ${context.originAO?.business_name}. Destination: ${context.destAO?.business_name}`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            origin_ao_name: context.originAO?.business_name,
            dest_ao_name: context.destAO?.business_name,
            tracking_code: context.shipment.tracking_code,
            weight: context.shipment.weight,
            size: context.shipment.size,
          },
        });
      }

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      console.log(
        `✅ Sent ${notifications.length} QR assignment notifications`
      );
    } catch (error) {
      console.error("❌ Failed to send QR assignment notifications:", error);
      // Don't throw error to avoid breaking the main QR assignment flow
    }
  }

  /**
   * Send notifications for DROPOFF scan operation
   */
  async notifyDropoffScan(context: ShipmentNotificationContext): Promise<void> {
    try {
      console.log(
        `📧 Sending dropoff scan notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - Package successfully dropped off
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.SHIPMENT_DROPPED_OFF,
        title: "📤 Package Dropped Off",
        message: `Your package has been successfully dropped off at ${context.originAO?.business_name}. It's now awaiting pickup by a car operator.`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          origin_ao_name: context.originAO?.business_name,
          dropped_off_at: new Date().toISOString(),
        },
      });

      // 2. Notify Origin AO - Package received and stored
      if (context.originAO && context.actionUserId !== context.originAO.id) {
        notifications.push({
          userId: context.originAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_DROPPED_OFF,
          title: "📦 Package Received",
          message: `Package from ${context.customer?.name} has been received and stored. Ready for car operator pickup.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            customer_name: context.customer?.name,
            tracking_code: context.shipment.tracking_code,
            dropped_off_at: new Date().toISOString(),
          },
        });
      }

      // 3. Notify eligible Car Operators - Package available for pickup
      const eligibleCarOperators = await this.getEligibleCarOperators(
        context.originAO?.id,
        context.destAO?.id
      );

      for (const carOperator of eligibleCarOperators) {
        notifications.push({
          userId: carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.PACKAGE_READY_FOR_PICKUP,
          title: "📦 Package Available for Pickup",
          message: `Package is now available for pickup at ${context.originAO?.business_name}. Destination: ${context.destAO?.business_name}`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            origin_ao_name: context.originAO?.business_name,
            dest_ao_name: context.destAO?.business_name,
            tracking_code: context.shipment.tracking_code,
            weight: context.shipment.weight,
            size: context.shipment.size,
          },
        });
      }

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      console.log(`✅ Sent ${notifications.length} dropoff scan notifications`);
    } catch (error) {
      console.error("❌ Failed to send dropoff scan notifications:", error);
    }
  }

  /**
   * Send notifications for PICKUP scan operation
   */
  async notifyPickupScan(context: ShipmentNotificationContext): Promise<void> {
    try {
      console.log(
        `📧 Sending pickup scan notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - Package picked up by transport
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.SHIPMENT_PICKED_UP,
        title: "🚚 Package Picked Up",
        message: `Your package has been picked up by a car operator from ${context.originAO?.business_name} and is now in transit to ${context.destAO?.business_name}.`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          origin_ao_name: context.originAO?.business_name,
          dest_ao_name: context.destAO?.business_name,
          car_operator_name: context.carOperator?.name,
          picked_up_at: new Date().toISOString(),
        },
      });

      // 2. Notify Origin AO - Package collected by CO
      if (context.originAO && context.actionUserId !== context.originAO.id) {
        notifications.push({
          userId: context.originAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_PICKED_UP,
          title: "🚚 Package Collected",
          message: `Package has been collected by car operator ${
            context.carOperator?.name || "Unknown"
          }. Package is now in transit.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            tracking_code: context.shipment.tracking_code,
            car_operator_name: context.carOperator?.name,
            picked_up_at: new Date().toISOString(),
          },
        });
      }

      // 3. Notify Car Operator - Pickup confirmed (if different from action user)
      if (
        context.carOperator &&
        context.actionUserId !== context.carOperator.id
      ) {
        notifications.push({
          userId: context.carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_PICKED_UP,
          title: "📦 Pickup Confirmed",
          message: `Package pickup confirmed from ${context.originAO?.business_name}. Destination: ${context.destAO?.business_name}`,
          priority: NotificationPriority.HIGH,
          metadata: {
            tracking_code: context.shipment.tracking_code,
            origin_ao_name: context.originAO?.business_name,
            dest_ao_name: context.destAO?.business_name,
            picked_up_at: new Date().toISOString(),
          },
        });
      }

      // 4. Notify Destination AO - Package in transit, prepare for arrival
      if (context.destAO && context.destAO.id !== context.originAO?.id) {
        notifications.push({
          userId: context.destAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_IN_TRANSIT,
          title: "🚛 Package In Transit",
          message: `Package for ${context.shipment.receiver_name} is now in transit from ${context.originAO?.business_name}. Prepare for arrival.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            tracking_code: context.shipment.tracking_code,
            origin_ao_name: context.originAO?.business_name,
            car_operator_name: context.carOperator?.name,
            picked_up_at: new Date().toISOString(),
          },
        });
      }

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      console.log(`✅ Sent ${notifications.length} pickup scan notifications`);
    } catch (error) {
      console.error("❌ Failed to send pickup scan notifications:", error);
    }
  }

  /**
   * Send notifications for ARRIVAL scan operation
   */
  async notifyArrivalScan(context: ShipmentNotificationContext): Promise<void> {
    try {
      console.log(
        `📧 Sending arrival scan notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - Package arrived at destination
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.SHIPMENT_ARRIVED,
        title: "📍 Package Arrived",
        message: `Your package has arrived at ${context.destAO?.business_name}. The receiver ${context.shipment.receiver_name} can now collect it.`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          dest_ao_name: context.destAO?.business_name,
          receiver_name: context.shipment.receiver_name,
          receiver_phone: context.shipment.receiver_phone,
          arrived_at: new Date().toISOString(),
        },
      });

      // 2. Notify Car Operator - Delivery to AO confirmed
      if (
        context.carOperator &&
        context.actionUserId !== context.carOperator.id
      ) {
        notifications.push({
          userId: context.carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_ARRIVED,
          title: "✅ Delivery Confirmed",
          message: `Package delivery to ${context.destAO?.business_name} has been confirmed. Transport completed successfully.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            tracking_code: context.shipment.tracking_code,
            dest_ao_name: context.destAO?.business_name,
            arrived_at: new Date().toISOString(),
          },
        });
      }

      // 3. Notify Destination AO - Package received, ready for customer pickup
      if (context.destAO && context.actionUserId !== context.destAO.id) {
        notifications.push({
          userId: context.destAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_READY_FOR_DELIVERY,
          title: "📦 Package Ready for Delivery",
          message: `Package for ${context.shipment.receiver_name} has been received and is ready for customer pickup.`,
          priority: NotificationPriority.HIGH,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            receiver_phone: context.shipment.receiver_phone,
            tracking_code: context.shipment.tracking_code,
            arrived_at: new Date().toISOString(),
          },
        });
      }

      // 4. Notify Receiver - Package ready for collection (if we have receiver contact info)
      // Note: This would require SMS service integration for receiver_phone
      // For now, we'll create a notification for the customer to inform the receiver

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      console.log(`✅ Sent ${notifications.length} arrival scan notifications`);
    } catch (error) {
      console.error("❌ Failed to send arrival scan notifications:", error);
    }
  }

  /**
   * Send notifications for final delivery operation
   */
  async notifyDelivery(context: ShipmentNotificationContext): Promise<void> {
    try {
      console.log(
        `📧 Sending delivery notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - Package successfully delivered
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.SHIPMENT_DELIVERED,
        title: "🎉 Package Delivered",
        message: `Your package has been successfully delivered to ${context.shipment.receiver_name}. Thank you for using our service!`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          receiver_name: context.shipment.receiver_name,
          dest_ao_name: context.destAO?.business_name,
          delivered_at: new Date().toISOString(),
        },
      });

      // 2. Notify Destination AO - Delivery completed
      if (context.destAO && context.actionUserId !== context.destAO.id) {
        notifications.push({
          userId: context.destAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_DELIVERED,
          title: "✅ Delivery Completed",
          message: `Package for ${context.shipment.receiver_name} has been successfully delivered. Shipment completed.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            tracking_code: context.shipment.tracking_code,
            delivered_at: new Date().toISOString(),
          },
        });
      }

      // 3. Notify Origin AO - Shipment journey completed
      if (context.originAO && context.originAO.id !== context.destAO?.id) {
        notifications.push({
          userId: context.originAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_DELIVERED,
          title: "🎉 Shipment Journey Completed",
          message: `Package from your location has been successfully delivered to ${context.shipment.receiver_name} at ${context.destAO?.business_name}.`,
          priority: NotificationPriority.LOW,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            tracking_code: context.shipment.tracking_code,
            dest_ao_name: context.destAO?.business_name,
            delivered_at: new Date().toISOString(),
          },
        });
      }

      // 4. Notify Car Operator - Transport completed successfully (if involved)
      if (context.carOperator) {
        notifications.push({
          userId: context.carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_DELIVERED,
          title: "🚚 Transport Completed",
          message: `Package you transported has been successfully delivered to ${context.shipment.receiver_name}. Great job!`,
          priority: NotificationPriority.LOW,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            tracking_code: context.shipment.tracking_code,
            dest_ao_name: context.destAO?.business_name,
            delivered_at: new Date().toISOString(),
          },
        });
      }

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      // Send delivery completion emails
      await this.sendDeliveryCompletionEmails(context);

      console.log(`✅ Sent ${notifications.length} delivery notifications`);
    } catch (error) {
      console.error("❌ Failed to send delivery notifications:", error);
    }
  }

  /**
   * Send delivery completion emails
   */
  private async sendDeliveryCompletionEmails(
    context: ShipmentNotificationContext
  ): Promise<void> {
    try {
      // Send email to customer
      if (context.customer?.email) {
        await this.emailService.sendShipmentStatusEmail(
          context.customer.email,
          context.customer.name,
          context.shipment.id,
          "DELIVERED",
          context.shipment.tracking_code,
          {
            receiver_name: context.shipment.receiver_name,
            dest_ao_name: context.destAO?.business_name,
            delivered_at: new Date().toISOString(),
          }
        );
      }

      console.log("✅ Sent delivery completion emails");
    } catch (error) {
      console.error("❌ Failed to send delivery completion emails:", error);
    }
  }

  /**
   * Send notifications for general status changes
   */
  async notifyStatusChange(
    context: ShipmentNotificationContext,
    oldStatus: string,
    newStatus: string,
    cancellationReason?: string
  ): Promise<void> {
    try {
      console.log(
        `📧 Sending status change notifications for shipment ${context.shipment.id}: ${oldStatus} → ${newStatus}`
      );

      const notifications: CreateNotificationData[] = [];
      const notificationType = this.getNotificationTypeForStatus(newStatus);
      const priority = this.getPriorityForStatus(newStatus);

      // Get all interested parties
      const interestedParties = this.getInterestedParties(context);

      for (const party of interestedParties) {
        const message = this.getStatusChangeMessage(
          party.userType,
          oldStatus,
          newStatus,
          context,
          cancellationReason
        );

        notifications.push({
          userId: party.userId,
          shipmentId: context.shipment.id,
          type: notificationType,
          title: this.getStatusChangeTitle(newStatus),
          message,
          priority,
          metadata: {
            tracking_code: context.shipment.tracking_code,
            old_status: oldStatus,
            new_status: newStatus,
            cancellation_reason: cancellationReason,
            changed_at: new Date().toISOString(),
            ...context.metadata,
          },
        });
      }

      // Send all notifications
      if (notifications.length > 0) {
        await this.notificationService.createBulkNotifications(notifications);
      }

      console.log(
        `✅ Sent ${notifications.length} status change notifications`
      );
    } catch (error) {
      console.error("❌ Failed to send status change notifications:", error);
    }
  }

  /**
   * Get notification type based on shipment status
   */
  private getNotificationTypeForStatus(status: string): NotificationType {
    switch (status) {
      case "PENDING":
        return NotificationType.SHIPMENT_CREATED;
      case "AWAITING_PICKUP":
        return NotificationType.SHIPMENT_DROPPED_OFF;
      case "PICKED_UP_BY_CO":
        return NotificationType.SHIPMENT_PICKED_UP;
      case "IN_TRANSIT":
        return NotificationType.SHIPMENT_IN_TRANSIT;
      case "ARRIVED_AT_DESTINATION":
        return NotificationType.SHIPMENT_ARRIVED;
      case "READY_FOR_DELIVERY":
        return NotificationType.SHIPMENT_READY_FOR_DELIVERY;
      case "DELIVERED":
        return NotificationType.SHIPMENT_DELIVERED;
      case "CANCELLED":
        return NotificationType.SHIPMENT_CANCELLED;
      default:
        return NotificationType.SHIPMENT_STATUS_CHANGED;
    }
  }

  /**
   * Get priority based on shipment status
   */
  private getPriorityForStatus(status: string): NotificationPriority {
    switch (status) {
      case "DELIVERED":
      case "CANCELLED":
      case "READY_FOR_DELIVERY":
        return NotificationPriority.HIGH;
      case "PICKED_UP_BY_CO":
      case "ARRIVED_AT_DESTINATION":
        return NotificationPriority.NORMAL;
      default:
        return NotificationPriority.NORMAL;
    }
  }

  /**
   * Get interested parties for notifications
   */
  private getInterestedParties(
    context: ShipmentNotificationContext
  ): Array<{ userId: string; userType: string }> {
    const parties: Array<{ userId: string; userType: string }> = [];

    // Customer is always interested
    parties.push({
      userId: context.shipment.customer_id,
      userType: "CUSTOMER",
    });

    // Origin AO
    if (context.originAO) {
      parties.push({
        userId: context.originAO.id,
        userType: "ACCESS_OPERATOR",
      });
    }

    // Destination AO (if different from origin)
    if (context.destAO && context.destAO.id !== context.originAO?.id) {
      parties.push({
        userId: context.destAO.id,
        userType: "ACCESS_OPERATOR",
      });
    }

    // Car Operator (if assigned)
    if (context.carOperator) {
      parties.push({
        userId: context.carOperator.id,
        userType: "CAR_OPERATOR",
      });
    }

    return parties;
  }

  /**
   * Get status change message for specific user type
   */
  private getStatusChangeMessage(
    userType: string,
    oldStatus: string,
    newStatus: string,
    context: ShipmentNotificationContext,
    cancellationReason?: string
  ): string {
    const trackingCode = context.shipment.tracking_code;

    if (newStatus === "CANCELLED") {
      const reasonMap: Record<string, string> = {
        USER_CANCELLED: "by customer request",
        SYSTEM_EXPIRED: "due to expiration",
        ADMIN_CANCELLED: "by administrator",
      };

      const reason =
        reasonMap[cancellationReason ?? ""] ?? "for unknown reason";

      switch (userType) {
        case "CUSTOMER":
          return `Your shipment (${trackingCode}) has been cancelled ${reason}.`;
        case "ACCESS_OPERATOR":
          return `Shipment ${trackingCode} has been cancelled ${reason}.`;
        case "CAR_OPERATOR":
          return `Transport opportunity for shipment ${trackingCode} has been cancelled ${reason}.`;
        default:
          return `Shipment ${trackingCode} has been cancelled ${reason}.`;
      }
    }

    // Status-specific messages
    switch (newStatus) {
      case "DELIVERED":
        return userType === "CUSTOMER"
          ? `Your package has been delivered to ${context.shipment.receiver_name}!`
          : `Package ${trackingCode} has been successfully delivered.`;

      case "READY_FOR_DELIVERY":
        return userType === "CUSTOMER"
          ? `Your package is ready for pickup at ${context.destAO?.business_name}.`
          : `Package ${trackingCode} is ready for customer pickup.`;

      case "ARRIVED_AT_DESTINATION":
        return userType === "CUSTOMER"
          ? `Your package has arrived at ${context.destAO?.business_name}.`
          : `Package ${trackingCode} has arrived at destination.`;

      case "IN_TRANSIT":
        return userType === "CUSTOMER"
          ? `Your package is now in transit to ${context.destAO?.business_name}.`
          : `Package ${trackingCode} is now in transit.`;

      case "PICKED_UP_BY_CO":
        return userType === "CUSTOMER"
          ? `Your package has been picked up for transport.`
          : `Package ${trackingCode} has been picked up by car operator.`;

      case "AWAITING_PICKUP":
        return userType === "CUSTOMER"
          ? `Your package has been received and is awaiting pickup.`
          : `Package ${trackingCode} is ready for car operator pickup.`;

      default:
        return `Shipment ${trackingCode} status updated to ${newStatus
          .toLowerCase()
          .replace(/_/g, " ")}.`;
    }
  }

  /**
   * Get status change title
   */
  private getStatusChangeTitle(status: string): string {
    switch (status) {
      case "DELIVERED":
        return "🎉 Package Delivered";
      case "READY_FOR_DELIVERY":
        return "✅ Ready for Pickup";
      case "ARRIVED_AT_DESTINATION":
        return "📍 Package Arrived";
      case "IN_TRANSIT":
        return "🚛 In Transit";
      case "PICKED_UP_BY_CO":
        return "🚚 Package Picked Up";
      case "AWAITING_PICKUP":
        return "📦 Awaiting Pickup";
      default:
        return "🔄 Status Updated";
    }
  }

  /**
   * Send notifications when a new shipment is created
   */
  async notifyShipmentCreated(
    context: ShipmentNotificationContext
  ): Promise<void> {
    try {
      console.log(
        `📧 Sending shipment creation notifications for shipment ${context.shipment.id}`
      );

      const notifications: CreateNotificationData[] = [];

      // 1. Notify Customer - Confirmation with pickup code and timer
      notifications.push({
        userId: context.shipment.customer_id,
        shipmentId: context.shipment.id,
        type: NotificationType.SHIPMENT_CREATED,
        title: "📦 Shipment Created Successfully",
        message: `Your shipment has been created! You have 24 hours to drop it off at ${
          context.originAO?.business_name || "the origin access point"
        }. Pickup code: ${context.shipment.pickup_code}`,
        priority: NotificationPriority.HIGH,
        metadata: {
          tracking_code: context.shipment.tracking_code,
          pickup_code: context.shipment.pickup_code,
          origin_ao_name: context.originAO?.business_name,
          dest_ao_name: context.destAO?.business_name,
          expires_at: context.shipment.expires_at,
        },
      });

      // 2. Notify Origin AO - New shipment incoming
      if (context.originAO) {
        notifications.push({
          userId: context.originAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_CREATED,
          title: "📦 New Shipment Incoming",
          message: `Customer ${
            context.customer?.name || "Unknown"
          } created a shipment for drop-off at your location. Expected within 24 hours.`,
          priority: NotificationPriority.NORMAL,
          metadata: {
            customer_name: context.customer?.name,
            customer_phone: context.customer?.phone,
            tracking_code: context.shipment.tracking_code,
            weight: context.shipment.weight,
            size: context.shipment.size,
            description: context.shipment.description,
          },
        });
      }

      // 3. Notify Destination AO - Future delivery notification
      if (context.destAO && context.destAO.id !== context.originAO?.id) {
        notifications.push({
          userId: context.destAO.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_CREATED,
          title: "📦 Future Delivery Scheduled",
          message: `A shipment has been created for delivery to your location. Receiver: ${context.shipment.receiver_name}`,
          priority: NotificationPriority.LOW,
          metadata: {
            receiver_name: context.shipment.receiver_name,
            receiver_phone: context.shipment.receiver_phone,
            tracking_code: context.shipment.tracking_code,
            origin_ao_name: context.originAO?.business_name,
          },
        });
      }

      // 4. Notify eligible Car Operators - New transport opportunity
      const eligibleCarOperators = await this.getEligibleCarOperators(
        context.originAO?.id,
        context.destAO?.id
      );

      for (const carOperator of eligibleCarOperators) {
        notifications.push({
          userId: carOperator.id,
          shipmentId: context.shipment.id,
          type: NotificationType.SHIPMENT_CREATED,
          title: "🚚 New Transport Opportunity",
          message: `New shipment available for transport from ${context.originAO?.business_name} to ${context.destAO?.business_name}`,
          priority: NotificationPriority.LOW,
          metadata: {
            origin_ao_name: context.originAO?.business_name,
            dest_ao_name: context.destAO?.business_name,
            tracking_code: context.shipment.tracking_code,
            weight: context.shipment.weight,
            size: context.shipment.size,
          },
        });
      }

      // Send all notifications
      await this.notificationService.createBulkNotifications(notifications);

      // Send email notifications
      await this.sendShipmentCreatedEmails(context);

      console.log(
        `✅ Sent ${notifications.length} shipment creation notifications`
      );
    } catch (error) {
      console.error(
        "❌ Failed to send shipment creation notifications:",
        error
      );
      // Don't throw error to avoid breaking the main shipment creation flow
    }
  }

  /**
   * Get eligible car operators for a route
   */
  private async getEligibleCarOperators(
    originAoId?: string,
    destAoId?: string
  ): Promise<any[]> {
    if (!originAoId || !destAoId) return [];

    try {
      const carOperators = await this.prisma.carOperator.findMany({
        where: {
          approved: true,
          user: {
            status: "ACTIVE",
          },
          OR: [
            // Exact match: pickup from origin, dropoff at destination
            {
              AND: [
                { pickup_access_point_id: originAoId },
                { dropoff_access_point_id: destAoId },
              ],
            },
            // Reverse match: pickup from destination, dropoff at origin
            // This allows COs to work in both directions between their 2 AOs
            {
              AND: [
                { pickup_access_point_id: destAoId },
                { dropoff_access_point_id: originAoId },
              ],
            },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        take: 10, // Limit to avoid spam
      });

      return carOperators.map((co) => ({
        id: co.user.id,
        name: co.user.name,
        email: co.user.email,
      }));
    } catch (error) {
      console.error("❌ Failed to get eligible car operators:", error);
      return [];
    }
  }

  /**
   * Send email notifications for shipment creation
   */
  private async sendShipmentCreatedEmails(
    context: ShipmentNotificationContext
  ): Promise<void> {
    try {
      // Send email to customer
      if (context.customer?.email) {
        await this.emailService.sendShipmentCreatedEmail(
          context.customer.email,
          context.customer.name,
          context.shipment.id,
          context.shipment.pickup_code
        );
      }

      // Send email to origin AO
      if (context.originAO?.user?.email) {
        await this.emailService.sendNewShipmentNotificationToAO(
          context.originAO.user.email,
          context.originAO.business_name,
          context.shipment.id
        );
      }

      console.log("✅ Sent shipment creation emails");
    } catch (error) {
      console.error("❌ Failed to send shipment creation emails:", error);
      // Don't throw to avoid breaking notification flow
    }
  }

  /**
   * Get full shipment context with related entities
   */
  async getShipmentContext(
    shipmentId: string
  ): Promise<ShipmentNotificationContext | null> {
    try {
      const shipment = await this.prisma.shipment.findUnique({
        where: { id: shipmentId },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          originAO: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          destAO: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          assignedCarOperator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!shipment) return null;

      return {
        shipment,
        customer: shipment.customer,
        originAO: shipment.originAO,
        destAO: shipment.destAO,
        carOperator: shipment.assignedCarOperator,
      };
    } catch (error) {
      console.error("❌ Failed to get shipment context:", error);
      return null;
    }
  }
}
