# Modern Email Templates with Green/Blue Gradient

This document describes the new modern email templates implemented for the Shipment Relay Platform, featuring bilingual support (Arabic + English) with custom fonts and a beautiful green/blue gradient design.

**Note:** All references to "DHL" have been removed from the codebase. The templates now use appropriate naming conventions.

## 🎨 Features

- **Modern Design**: Professional layout with green/blue gradient theme
- **Universal Application**: Applied to ALL emails (authentication, shipments, notifications, admin)
- **Bilingual Support**: Arabic and English content in the same email
- **Custom Fonts**:
  - **Cairo** font for Arabic text
  - **Roboto Condensed** font for English text
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Brand Colors**: Modern green/blue gradient (#4CAF50 green, #2196F3 blue)
- **Status Badges**: Color-coded status indicators
- **Modern Layout**: Card-based design with proper spacing and typography

## 📧 Available Templates

### Authentication & User Management
1. **Welcome Email** - Sent when users register
2. **Email Verification (OTP)** - Email verification with one-time password
3. **Password Reset** - Password reset with secure link
4. **Password Changed** - Confirmation when password is updated
5. **Account Activated** - Notification when account is approved

### Shipment Operations
1. **Shipment Created** - Sent when a new shipment is created
2. **Shipment Status Update** - Sent when shipment status changes
3. **Shipment Expired** - Notification for expired shipments

### Notifications
1. **New Shipment (AO)** - Notify Access Operators of new shipments
2. **Available Shipment (CO)** - Notify Car Operators of available shipments

### Admin Emails
1. **Admin Welcome** - Welcome email for admin users
2. **Admin OTP** - Admin verification emails
3. **Admin Password Reset/Changed** - Admin password management

## 🚀 Usage

### Basic Usage

```typescript
import { EmailService } from '../services/EmailService';

const emailService = new EmailService();

// Send DHL-style shipment created email
await emailService.sendDhlStyleShipmentCreatedEmail(
  '<EMAIL>',
  'يوسف حبيب', // Customer name (supports Arabic)
  'SH-2025-001234',
  'ABC123', // Pickup code
  '**********' // Tracking code
);

// Send DHL-style status update email
await emailService.sendDhlStyleShipmentStatusUpdateEmail(
  '<EMAIL>',
  'يوسف حبيب',
  'SH-2025-001234',
  'IN_TRANSIT',
  '**********',
  'Package is on its way to destination city'
);
```

### Advanced Usage with Enhanced Notification Service

```typescript
import { EnhancedShipmentNotificationService } from '../examples/dhlStyleEmailIntegration';

const notificationService = new EnhancedShipmentNotificationService();

// Send multi-user notifications (customer, AO, CO)
await notificationService.sendMultiUserShipmentNotification(
  'SH-2025-001234',
  'customer-id-123',
  'ao-id-456',
  'co-id-789',
  'STATUS_UPDATE',
  {
    status: 'DELIVERED',
    trackingCode: '**********',
    details: 'Package delivered successfully'
  }
);
```

## 🌐 Language Support

The system supports three email formats:

1. **BILINGUAL** (default): Shows both Arabic and English content
2. **ARABIC_ONLY**: Shows only Arabic content
3. **ENGLISH_ONLY**: Shows only English content

User preferences are automatically detected from the database:

```typescript
// User preferences are stored in notification_preferences table
{
  preferred_language: 'ARABIC' | 'ENGLISH',
  email_format: 'BILINGUAL' | 'ARABIC_ONLY' | 'ENGLISH_ONLY'
}
```

## 🎯 Supported Shipment Statuses

The templates support all shipment statuses with proper Arabic translations:

- `PENDING` → في الانتظار
- `AWAITING_PICKUP` → في انتظار الاستلام
- `PICKED_UP_BY_CO` → تم الاستلام من قبل مشغل المركبة
- `IN_TRANSIT` → في الطريق
- `ARRIVED_AT_DESTINATION` → وصل إلى الوجهة
- `READY_FOR_DELIVERY` → جاهز للتسليم
- `DELIVERED` → تم التسليم
- `CANCELLED` → ملغي

## 🛠️ Technical Implementation

### File Structure

```
src/
├── utils/
│   ├── dhlStyleEmailTemplates.ts     # Main DHL-style templates
│   ├── arabicEmailTemplates.ts       # Arabic content functions
│   └── bilingualEmailTemplates.ts    # Existing bilingual system
├── services/
│   └── EmailService.ts               # Enhanced with DHL methods
├── interfaces/
│   └── IEmailService.ts              # Updated interface
├── examples/
│   └── dhlStyleEmailIntegration.ts   # Integration examples
└── scripts/
    └── testDhlStyleEmails.ts         # Test script
```

### Key Functions

#### Arabic Content Functions
```typescript
// Generate Arabic content for shipment updates
dhlStyleShipmentUpdateArabicContent(name, shipmentId, status, trackingCode, details?)

// Generate Arabic content for shipment creation
dhlStyleShipmentCreatedArabicContent(name, shipmentId, pickupCode, trackingCode)
```

#### English Content Functions
```typescript
// Generate English content for shipment updates
dhlStyleShipmentUpdateEnglishContent(name, shipmentId, status, trackingCode, details?)

// Generate English content for shipment creation
dhlStyleShipmentCreatedEnglishContent(name, shipmentId, pickupCode, trackingCode)
```

#### Template Generators
```typescript
// Generate complete email with subject and HTML
generateDhlStyleShipmentCreatedEmail(name, shipmentId, pickupCode, trackingCode, options)
generateDhlStyleShipmentUpdateEmail(name, shipmentId, status, trackingCode, options, details?)
```

## 🧪 Testing

### Generate Test Emails

Run the test script to generate sample emails:

```bash
npx ts-node src/scripts/testDhlStyleEmails.ts
```

This generates HTML files in the `test-emails/` directory:
- `dhl-style-shipment-created-bilingual.html`
- `dhl-style-shipment-update-bilingual.html`
- `dhl-style-shipment-created-arabic.html`
- `dhl-style-shipment-update-english.html`
- `dhl-style-comprehensive-demo.html`

### View Generated Emails

Open the generated HTML files in a browser to preview the email templates.

## ⚙️ Configuration

### Environment Variables

```env
# Enable DHL-style emails (default: true)
USE_DHL_STYLE_EMAILS=true

# Email service configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-email
SMTP_PASS=your-password
```

### Customization

You can customize the brand colors and fonts by modifying the template functions:

```typescript
// Custom brand color (default: #D40511)
const customTemplate = dhlStyleBilingualTemplate(
  arabicContent, 
  englishContent, 
  '#YOUR_BRAND_COLOR'
);
```

## 🔄 Migration from Existing Templates

The new DHL-style templates are designed to work alongside existing templates:

1. **Gradual Migration**: You can enable DHL-style emails for specific users or shipments
2. **Fallback Support**: If DHL-style email fails, it automatically falls back to regular templates
3. **Backward Compatibility**: All existing email functionality remains unchanged

## 📱 Mobile Responsiveness

The templates are optimized for mobile devices:
- Responsive design that adapts to screen size
- Touch-friendly buttons and links
- Proper font scaling for mobile viewing
- Optimized for both portrait and landscape orientations

## 🎨 Design Elements

- **Header**: Gradient background with brand colors
- **Language Selector**: Clear indication of bilingual content
- **Content Cards**: Separated sections for Arabic and English
- **Status Badges**: Color-coded status indicators
- **Information Tables**: Clean, structured data presentation
- **Call-to-Action**: Prominent buttons and important information
- **Footer**: Professional footer with contact information

## 🔧 Troubleshooting

### Common Issues

1. **Fonts not loading**: Ensure Google Fonts are accessible
2. **Arabic text not displaying correctly**: Check UTF-8 encoding
3. **Email client compatibility**: Test with major email clients
4. **Template generation errors**: Check console logs for detailed error messages

### Debug Mode

Enable debug logging in EmailService:

```typescript
console.log('📧 Sending DHL-style email:', {
  to: email,
  subject: emailTemplate.subject,
  template: 'DHL_STYLE'
});
```

## 📞 Support

For questions or issues with the DHL-style email templates:

1. Check the generated test emails for reference
2. Review the integration examples
3. Consult the existing bilingual email system documentation
4. Contact the development team for assistance
