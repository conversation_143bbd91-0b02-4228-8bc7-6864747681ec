/* eslint-disable no-nested-ternary */
import { Request, Response, NextFunction } from "express";

import {
  UUIDSchema,
  ShipmentCreateSchema,
  ShipmentQuerySchema,
  ShipmentScanRequestSchema,
  ShipmentDeliveryRequestSchema,
} from "../schemas";
import { ServiceFactory } from "../services/ServiceFactory";
import {
  CancellationReason,
  UserType,
  ShipmentStatus,
  Shipment,
  QRLabelStatus,
} from "../types/models";
import { validateRequest, AppError } from "../utils/errors";

export class ShipmentController {
  /**
   * Validate create shipment request
   */
  static validateCreateShipment = validateRequest(ShipmentCreateSchema);

  /**
   * Validate get my shipments query parameters
   */
  static validateGetMyShipments = validateRequest(ShipmentQuerySchema, "query");

  /**
   * Create a new shipment
   */
  static async createShipment(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated and is a customer
      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      if (userType !== UserType.CUSTOMER) {
        res.status(403).json({ error: "Only customers can create shipments" });
        return;
      }

      const {
        origin_ao_id,
        dest_ao_id,
        weight,
        size,
        description,
        receiver_name,
        receiver_phone,
      } = req.body;

      // Validate input
      if (
        !origin_ao_id ||
        !dest_ao_id ||
        !weight ||
        !size ||
        !description ||
        !receiver_name ||
        !receiver_phone
      ) {
        res.status(400).json({ error: "Missing required fields" });
        return;
      }

      // Create the shipment
      const shipmentService = ServiceFactory.getShipmentService();
      const shipment = await shipmentService.createShipment({
        customer_id: userId,
        origin_ao_id,
        dest_ao_id,
        weight: parseFloat(weight),
        size,
        description,
        receiver_name,
        receiver_phone,
        pickup_code: "", // Will be generated in the service
      });

      // Generate only pickup QR code - AO will generate their QR when customer arrives
      const qrCodes = {
        pickup_qr: {
          value: `PICKUP${shipment.pickup_code}`,
          description:
            "Keep this QR for package collection - show to final AO for delivery",
          contains: {
            shipment_id: shipment.id,
            pickup_code: shipment.pickup_code,
          },
        },
      };

      // Return shipment data with QR codes
      res.status(201).json({
        success: true,
        message:
          "Shipment created successfully. AO will generate their QR code when you arrive for drop-off.",
        data: {
          shipment,
          qr_codes: qrCodes,
        },
      });
    } catch (error) {
      console.error("Error creating shipment:", error);
      next(error);
    }
  }

  /**
   * Validate scan shipment request
   */
  static validateScanShipment = validateRequest(ShipmentScanRequestSchema);

  /**
   * Scan QR code for shipment workflow
   * POST /api/shipments/scan
   */
  static async scanShipment(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      console.info("=== SHIPMENT SCAN REQUEST START ===");
      console.info("Request headers:", req.headers);
      console.info("Request body keys:", Object.keys(req.body));
      console.info("Request body:", req.body);

      const userId = req.user?.id;
      const userType = req.user?.user_type;
      const { qr_value, photo_url, action, notes, shipment_id } = req.body;

      console.info("User info:", { userId, userType });
      console.info("User ID type:", typeof userId);
      console.info("Full req.user object:", req.user);
      console.info("Scan parameters:", {
        qr_value,
        photo_url,
        action,
        notes,
        shipment_id,
        hasQrValue: !!qr_value,
        hasPhotoUrl: !!photo_url,
        hasAction: !!action,
        hasShipmentId: !!shipment_id,
      });

      if (!userId) {
        console.error("ERROR: No userId found - unauthorized");
        throw AppError.unauthorized("Authentication required");
      }

      // Validate required fields (schema validation should catch this, but double-check)
      if (!qr_value || !photo_url || !action || !shipment_id) {
        console.error("ERROR: Missing required fields:", {
          qr_value: !!qr_value,
          photo_url: !!photo_url,
          action: !!action,
          shipment_id: !!shipment_id,
        });
        throw AppError.badRequest(
          "Missing required fields: qr_value, photo_url, action, shipment_id"
        );
      }

      console.info("Required fields validation passed");

      // Parse QR code to determine type and extract shipment info
      let shipmentId: string;

      console.info("Parsing QR code:", {
        qr_value,
        startsWithAO: qr_value.startsWith("AO_"),
        startsWithPICKUP: qr_value.startsWith("PICKUP"),
      });

      if (qr_value.startsWith("AO_")) {
        console.info("Processing AO QR code");
        // AO QR format: AO_randomString
        // Need to find the shipment associated with this QR code
        const qrLabel =
          await ServiceFactory.getQRLabelService().getQRLabelByValue(qr_value);
        console.info("QR Label lookup result:", {
          found: !!qrLabel,
          hasShipmentId: qrLabel?.shipment_id ? true : false,
          shipmentId: qrLabel?.shipment_id,
          status: qrLabel?.status,
        });

        if (!qrLabel) {
          console.error("ERROR: QR code not found in database");
          throw AppError.badRequest("QR code not found in database");
        }

        // If QR code is unused, auto-assign it to the provided shipment
        if (qrLabel.status === "UNUSED") {
          console.info("Auto-assigning unused QR code to shipment:", {
            qrValue: qr_value,
            shipmentId: shipment_id,
          });

          // Auto-assign the specific unused QR code to the specified shipment
          const qrLabelService = ServiceFactory.getQRLabelService();
          try {
            const assignedQRLabel =
              await qrLabelService.assignQRLabelToShipment(
                qrLabel.id,
                shipment_id,
                userId
              );
            console.info("QR code auto-assigned successfully:", {
              qrLabelId: assignedQRLabel.id,
              qrValue: assignedQRLabel.qr_value,
              shipmentId: assignedQRLabel.shipment_id,
            });

            // Update the qrLabel reference to the newly assigned one
            qrLabel.status = QRLabelStatus.ASSIGNED;
            qrLabel.shipment_id = shipment_id;
            qrLabel.assigned_at = new Date();
          } catch (assignError) {
            console.error(
              "ERROR: Failed to auto-assign QR code to shipment:",
              assignError
            );
            throw AppError.badRequest(
              `Failed to auto-assign QR code to shipment: ${
                assignError instanceof Error
                  ? assignError.message
                  : "Unknown error"
              }`
            );
          }
        }

        // Verify QR code is assigned to the correct shipment
        if (!qrLabel.shipment_id) {
          console.error("ERROR: QR code is not assigned to a shipment");
          throw AppError.badRequest("QR code is not assigned to a shipment");
        }

        // Validate that the QR code is assigned to the correct shipment
        if (qrLabel.shipment_id !== shipment_id) {
          console.error("ERROR: QR code is assigned to a different shipment:", {
            qrCodeShipmentId: qrLabel.shipment_id,
            requestedShipmentId: shipment_id,
          });
          throw AppError.badRequest(
            `QR code is assigned to a different shipment. Expected: ${shipment_id}, but QR code is assigned to: ${qrLabel.shipment_id}`
          );
        }

        shipmentId = qrLabel.shipment_id;
      } else if (qr_value.startsWith("PICKUP")) {
        console.info("Processing PICKUP QR code");
        // Pickup QR format: PICKUP123456
        const pickupCode = qr_value.substring(6); // Remove 'PICKUP' prefix
        console.info("Extracted pickup code:", pickupCode);

        const shipment =
          await ServiceFactory.getShipmentService().getShipmentByPickupCode(
            pickupCode
          );
        console.info("Shipment lookup by pickup code result:", {
          found: !!shipment,
          shipmentId: shipment?.id,
        });

        if (!shipment) {
          console.error("ERROR: Invalid pickup code");
          throw AppError.badRequest("Invalid pickup code");
        }
        shipmentId = shipment.id;
      } else {
        console.error("ERROR: Invalid QR code format:", qr_value);
        throw AppError.badRequest(
          "Invalid QR code format. Expected AO_randomString or PICKUP123456"
        );
      }

      console.info("Successfully parsed QR code, shipmentId:", shipmentId);

      // Validate action based on user type
      console.info("Validating action permissions:", {
        action,
        userType,
        isDropoffValid:
          action === "DROPOFF" && userType === UserType.ACCESS_OPERATOR,
        isPickupValid:
          action === "PICKUP" && userType === UserType.CAR_OPERATOR,
        isArrivalValid:
          action === "ARRIVAL" && userType === UserType.ACCESS_OPERATOR,
      });

      if (action === "DROPOFF" && userType !== UserType.ACCESS_OPERATOR) {
        console.error("ERROR: Invalid user type for DROPOFF action");
        throw AppError.forbidden(
          "Only Access Operators can perform dropoff scans"
        );
      }
      if (action === "PICKUP" && userType !== UserType.CAR_OPERATOR) {
        console.error("ERROR: Invalid user type for PICKUP action");
        throw AppError.forbidden("Only Car Operators can perform pickup scans");
      }
      if (action === "ARRIVAL" && userType !== UserType.ACCESS_OPERATOR) {
        console.error("ERROR: Invalid user type for ARRIVAL action");
        throw AppError.forbidden(
          "Only Access Operators can perform arrival scans"
        );
      }

      console.info("Action validation passed");

      console.info("Calling shipment service with parameters:", {
        userId,
        userType,
        shipmentId,
        qr_value,
        photo_url,
        action,
        notes,
      });

      const shipmentService = ServiceFactory.getShipmentService();
      const result = await shipmentService.scanShipmentEnhanced(
        userId,
        userType!,
        shipmentId,
        qr_value,
        photo_url,
        action,
        notes
      );

      console.info("Shipment scan successful:", {
        message: result.message,
        shipmentId: result.shipment?.id,
      });

      console.info("=== SHIPMENT SCAN REQUEST END - SUCCESS ===");

      res.status(200).json({
        success: true,
        message: result.message,
        data: {
          shipment: result.shipment,
          photo_url,
        },
      });
    } catch (error) {
      console.error("=== SHIPMENT SCAN ERROR ===");
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace",
        type: typeof error,
        error,
      });
      next(error);
    }
  }

  /**
   * Validate delivery shipment request
   */
  static validateDeliverShipment = validateRequest(
    ShipmentDeliveryRequestSchema
  );

  /**
   * Final delivery with both QR codes
   * POST /api/shipments/deliver
   */
  static async deliverShipment(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;
      const { shipment_qr, pickup_qr, photo_url, notes, receiver_phone } =
        req.body;

      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        throw AppError.forbidden(
          "Access denied. ACCESS_OPERATOR access required."
        );
      }

      // Validate required fields (schema validation should catch this, but double-check)
      if (!shipment_qr || !pickup_qr || !photo_url) {
        throw AppError.badRequest(
          "Missing required fields: shipment_qr, pickup_qr, photo_url"
        );
      }

      // Parse shipment QR (AO QR format: AO_randomString)
      if (!shipment_qr.startsWith("AO_")) {
        throw AppError.badRequest(
          "Invalid shipment QR code format. Expected format: AO_randomString"
        );
      }

      // Get shipment from AO QR
      const qrLabel =
        await ServiceFactory.getQRLabelService().getQRLabelByValue(shipment_qr);
      if (!qrLabel || !qrLabel.shipment_id) {
        throw AppError.badRequest(
          "Shipment QR code not found or not assigned to a shipment"
        );
      }

      const shipmentId = qrLabel.shipment_id;
      const pickupCode = pickup_qr; // Accept as-is, no prefix required

      const shipmentService = ServiceFactory.getShipmentService();
      const result = await shipmentService.deliverShipment(
        userId,
        shipmentId,
        pickupCode,
        photo_url,
        notes,
        receiver_phone
      );

      res.status(200).json({
        success: true,
        message: result.message,
        data: {
          shipment: result.shipment,
          photo_url,
        },
      });
    } catch (error) {
      // Add detailed error logging
      const err = error as any;
      console.error("Error in deliverShipment:", {
        error,
        message: err?.message,
        stack: err?.stack,
        type: err?.type,
        statusCode: err?.statusCode,
        details: err?.details,
      });
      next(error);
    }
  }

  /**
   * Get shipment details
   */
  static async getShipment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated
      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const { id } = req.params;

      // Validate UUID format
      const uuidValidation = UUIDSchema.safeParse(id);
      if (!uuidValidation.success) {
        res.status(400).json({
          error: "Invalid shipment ID format",
          message: "Shipment ID must be a valid UUID format",
        });
        return;
      }

      // Get the shipment
      const shipmentService = ServiceFactory.getShipmentService();
      const shipment = await shipmentService.getShipmentById(id);

      if (!shipment) {
        res.status(404).json({ error: "Shipment not found" });
        return;
      }

      // Check if user has access to this shipment
      const hasAccess =
        (userType === UserType.CUSTOMER && shipment.customer_id === userId) ||
        (userType === UserType.ACCESS_OPERATOR &&
          (shipment.origin_ao_id === userId ||
            shipment.dest_ao_id === userId)) ||
        userType === UserType.CAR_OPERATOR; // Car operators can see all shipments they might transport

      if (!hasAccess) {
        res
          .status(403)
          .json({ error: "You do not have access to this shipment" });
        return;
      }

      // Return shipment data (handover events removed from schema)
      res.status(200).json({
        success: true,
        data: {
          shipment,
        },
      });
    } catch (error) {
      console.error("Error getting shipment:", error);
      res.status(500).json({ error: "Failed to get shipment" });
    }
  }

  /**
   * Get pending shipments for the authenticated user
   */
  static async getPendingShipments(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated
      if (!userId) {
        res.status(401).json({
          error: "Unauthorized",
          message: "Authentication required to access shipments",
        });
        return;
      }

      // Parse pagination parameters
      const page = req.query.page ? parseInt(req.query.page as string) : 0;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

      // Parse role parameter for Access Operators
      const role = req.query.role as string;

      // Validate pagination parameters
      if (page < 0) {
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: "Page number must be 0 or greater",
        });
        return;
      }

      if (limit < 1 || limit > 100) {
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: "Limit must be between 1 and 100",
        });
        return;
      }

      // Validate role parameter for Access Operators
      if (userType === UserType.ACCESS_OPERATOR && role && !['origin', 'destination', 'both'].includes(role)) {
        res.status(400).json({
          error: "Invalid role parameter",
          message: "Role must be 'origin', 'destination', or 'both'",
        });
        return;
      }

      const skip = page * limit;
      const take = limit;

      const shipmentService = ServiceFactory.getShipmentService();
      let result: { shipments: Shipment[]; total: number };

      // Get pending shipments based on user type
      if (userType === UserType.CUSTOMER) {
        result = await shipmentService.getPendingShipmentsByCustomerId(userId, {
          skip,
          take,
        });
      } else if (userType === UserType.ACCESS_OPERATOR) {
        result = await shipmentService.getPendingShipmentsByAccessOperatorId(
          userId,
          { skip, take, role }
        );
      } else {
        // For car operators, get pending shipments they are eligible to transport
        result = await shipmentService.getPendingShipmentsForCarOperator(
          userId,
          { skip, take }
        );
      }

      // Return shipments with pagination metadata
      res.status(200).json({
        success: true,
        data: {
          shipments: result.shipments,
          pagination: {
            page,
            limit,
            total: result.total,
            totalPages: Math.ceil(result.total / limit),
            hasNext: (page + 1) * limit < result.total,
            hasPrev: page > 0,
          },
        },
      });
    } catch (error) {
      console.error("Error getting pending shipments:", error);
      res.status(500).json({
        error: "Internal server error",
        message:
          "Failed to retrieve pending shipments. Please try again later.",
      });
    }
  }

  /**
   * Get shipments for the authenticated user
   */
  static async getMyShipments(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated
      if (!userId) {
        res.status(401).json({
          error: "Unauthorized",
          message: "Authentication required to access shipments",
        });
        return;
      }

      // Handle pagination parameters - support both page/pageSize and skip/take formats
      let skip: number;
      let take: number;
      let page: number;
      let pageSize: number;

      // Check if skip/take parameters are provided directly
      if (req.query.skip !== undefined || req.query.take !== undefined) {
        skip = req.query.skip ? parseInt(req.query.skip as string) : 0;
        take = req.query.take ? parseInt(req.query.take as string) : 10;

        // Handle negative skip gracefully
        if (skip < 0) {
          console.warn(`Negative skip value received: ${skip}, setting to 0`);
          skip = 0;
        }

        // Calculate page and pageSize for response pagination metadata
        pageSize = take;
        page = Math.floor(skip / pageSize);
      } else {
        // Use page/pageSize format
        page = req.query.page ? parseInt(req.query.page as string) : 0;
        pageSize = req.query.pageSize
          ? parseInt(req.query.pageSize as string)
          : req.query.limit
          ? parseInt(req.query.limit as string)
          : 10;

        skip = page * pageSize;
        take = pageSize;
        if (skip < 0) skip = 0;
      }

      // Parse filter and search parameters
      const search = req.query.search as string | undefined;
      const status = req.query.status as string | undefined;

      // Handle both sort formats: 'sort=field:direction' and 'sortBy=field&sortOrder=direction'
      let sort = req.query.sort as string | undefined;
      const sortBy = req.query.sortBy as string | undefined;
      const sortOrder = req.query.sortOrder as string | undefined;

      // Convert sortBy/sortOrder to sort format if provided
      if (sortBy && sortOrder && !sort) {
        sort = `${sortBy}:${sortOrder}`;
      } else if (sortBy && !sortOrder && !sort) {
        sort = `${sortBy}:desc`; // default to desc if only sortBy provided
      }

      let originAoId = req.query.originAoId as string | undefined;
      let destAoId = req.query.destAoId as string | undefined;

      // Support for filter object in query param (e.g. ?filter={...})
      if (req.query.filter) {
        try {
          const filterObj =
            typeof req.query.filter === "string"
              ? JSON.parse(req.query.filter)
              : req.query.filter;
          // Support both AND/OR and direct keys
          if (filterObj.AND && Array.isArray(filterObj.AND)) {
            for (const cond of filterObj.AND) {
              if (cond.origin_ao_id) originAoId = cond.origin_ao_id;
              if (cond.dest_ao_id) destAoId = cond.dest_ao_id;
            }
          }
          if (filterObj.OR && Array.isArray(filterObj.OR)) {
            for (const cond of filterObj.OR) {
              if (cond.origin_ao_id) originAoId = cond.origin_ao_id;
              if (cond.dest_ao_id) destAoId = cond.dest_ao_id;
            }
          }
          if (filterObj.origin_ao_id) originAoId = filterObj.origin_ao_id;
          if (filterObj.dest_ao_id) destAoId = filterObj.dest_ao_id;
        } catch (e) {
          console.error("Failed to parse filter param:", req.query.filter, e);
        }
      }

      // Note: skip validation is now redundant since we handle negative values above
      // But keeping it as a safety check
      if (skip < 0) {
        console.error("ERROR: Invalid skip value after processing:", skip);
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: "Skip value must be 0 or greater",
        });
        return;
      }

      if (take < 1 || take > 100) {
        console.error("ERROR: Invalid take/pageSize value:", take);
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: "Take/pageSize must be between 1 and 100",
        });
        return;
      }

      // Log the processed parameters for debugging
      console.info("Processed query parameters:", {
        userId,
        userType,
        skip,
        take,
        search,
        status,
        sort,
        sortBy,
        sortOrder,
        originAoId,
        destAoId,
        filter: req.query.filter
      });

      const shipmentService = ServiceFactory.getShipmentService();
      let result: { shipments: Shipment[]; total: number; stats?: any };

      if (userType === UserType.CUSTOMER) {
        result = await shipmentService.getShipmentsByCustomerId(userId, {
          skip,
          take,
          search,
          status,
          sort,
          originAoId,
          destAoId,
        });
      } else if (userType === UserType.ACCESS_OPERATOR) {
        result = await shipmentService.getShipmentsByAccessOperatorId(userId, {
          skip,
          take,
          search,
          status,
          sort,
          originAoId,
          destAoId,
        });
      } else if (userType === UserType.CAR_OPERATOR) {
        // For car operators, get shipments they are eligible to transport based on their access points
        result = await shipmentService.getShipmentsByCarOperator(userId, {
          skip,
          take,
          search,
          status,
          sort,
          originAoId,
          destAoId,
        });
      } else {
        // Fallback for unknown user types
        result = { shipments: [], total: 0 };
      }

      console.info("Service returned:", {
        shipmentsCount: result.shipments.length,
        total: result.total,
      });

      // Return shipments with pagination metadata and stats
      res.status(200).json({
        success: true,
        data: {
          shipments: result.shipments,
          total: result.total,
          stats: result.stats,
          pagination: {
            page,
            limit: pageSize,
            total: result.total,
            totalPages: Math.ceil(result.total / pageSize),
            hasNext: (page + 1) * pageSize < result.total,
            hasPrev: page > 0,
          },
        },
      });
    } catch (error) {
      console.error("Error getting shipments:", error);

      // Handle Prisma validation errors specifically
      if (error instanceof Error && error.name === 'PrismaClientValidationError') {
        console.error("Prisma validation error:", error.message);
        res.status(400).json({
          error: "Invalid search parameters",
          message: "The search query contains invalid parameters. Please check your search terms and try again.",
          details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
        return;
      }

      res.status(500).json({
        error: "Internal server error",
        message: "Failed to retrieve shipments. Please try again later.",
      });
    }
  }

  /**
   * Cancel a shipment
   */
  static async cancelShipment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated and is a customer
      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const { id } = req.params;

      // Validate UUID format
      const uuidValidation = UUIDSchema.safeParse(id);
      if (!uuidValidation.success) {
        res.status(400).json({
          error: "Invalid shipment ID format",
          message: "Shipment ID must be a valid UUID format",
        });
        return;
      }

      // Get the shipment
      const shipmentService = ServiceFactory.getShipmentService();
      const shipment = await shipmentService.getShipmentById(id);

      if (!shipment) {
        res.status(404).json({ error: "Shipment not found" });
        return;
      }

      // Check if user is the shipment owner
      if (userType !== UserType.CUSTOMER || shipment.customer_id !== userId) {
        res
          .status(403)
          .json({ error: "Only the shipment owner can cancel it" });
        return;
      }

      // Check if shipment can be cancelled
      if (shipment.status !== ShipmentStatus.PENDING) {
        res
          .status(400)
          .json({ error: "Only pending shipments can be cancelled" });
        return;
      }

      // Cancel the shipment with user cancellation reason
      const shipmentStatusService = ServiceFactory.getShipmentStatusService();
      const updatedShipment =
        await shipmentStatusService.updateShipmentStatusWithReason(
          id,
          ShipmentStatus.CANCELLED,
          userId,
          CancellationReason.USER_CANCELLED,
          "Shipment cancelled by customer"
        );

      // Return updated shipment
      res.status(200).json({ shipment: updatedShipment });
    } catch (error) {
      console.error("Error cancelling shipment:", error);
      res.status(500).json({ error: "Failed to cancel shipment" });
    }
  }

  /**
   * Update a shipment
   */
  static async updateShipment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated and is a customer
      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      if (userType !== UserType.CUSTOMER) {
        res.status(403).json({ error: "Only customers can update shipments" });
        return;
      }

      const { id } = req.params;

      // Validate UUID format
      const uuidValidation = UUIDSchema.safeParse(id);
      if (!uuidValidation.success) {
        res.status(400).json({
          error: "Invalid shipment ID format",
          message: "Shipment ID must be a valid UUID format",
        });
        return;
      }

      // Get the shipment
      const shipmentService = ServiceFactory.getShipmentService();
      const shipment = await shipmentService.getShipmentById(id);

      if (!shipment) {
        res.status(404).json({ error: "Shipment not found" });
        return;
      }

      // Check if user is the shipment owner
      if (shipment.customer_id !== userId) {
        res
          .status(403)
          .json({ error: "Only the shipment owner can update it" });
        return;
      }

      // Only allow updating certain fields
      const allowedFields = [
        "origin_ao_id",
        "dest_ao_id",
        "weight",
        "size",
        "description",
      ];
      const updateData: any = {};
      for (const field of allowedFields) {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      }

      if (Object.keys(updateData).length === 0) {
        res.status(400).json({ error: "No valid fields to update" });
        return;
      }

      const updatedShipment = await shipmentService.updateShipment(
        id,
        updateData
      );
      res.status(200).json({ shipment: updatedShipment });
    } catch (error) {
      console.error("Error updating shipment:", error);
      res.status(500).json({ error: "Failed to update shipment" });
    }
  }
}
