import { Request, Response, NextFunction } from 'express';

import { AccessPointUpdateSchema } from '../schemas';
import { AccessOperatorService } from '../services/AccessOperatorService';
import { ServiceFactory } from '../services/ServiceFactory';
import { UserType } from '../types/models';
import { AppError, validateRequest } from '../utils/errors';
import { parsePaginationParams } from '../utils/pagination';

export class AccessOperatorController {
  /**
   * Create a new access point (Access Operator only)
   */
  static async createAccessPoint(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;

      // Verify user is authenticated and is an access operator
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
          error: {
            type: 'AUTHENTICATION_ERROR',
            details: 'User not authenticated',
          },
        });
        return;
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        res.status(403).json({
          success: false,
          message: 'Forbidden',
          error: {
            type: 'AUTHORIZATION_ERROR',
            details: 'Only access operators can create access points',
          },
        });
        return;
      }

      // Extract and validate request data
      const {
        name,
        email,
        password,
        phone,
        business_name,
        address,
        geo_latitude,
        geo_longitude,
        approved = true,
      } = req.body;

      // Validate required fields
      if (!name || !email || !password || !phone || !business_name || !address ||
          geo_latitude === undefined || geo_longitude === undefined) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields',
          error: {
            type: 'VALIDATION_ERROR',
            details: {
              name: !name ? ['Name is required'] : [],
              email: !email ? ['Email is required'] : [],
              password: !password ? ['Password is required'] : [],
              phone: !phone ? ['Phone is required'] : [],
              business_name: !business_name ? ['Business name is required'] : [],
              address: !address ? ['Address is required'] : [],
              geo_latitude: geo_latitude === undefined ? ['Latitude is required'] : [],
              geo_longitude: geo_longitude === undefined ? ['Longitude is required'] : [],
            },
          },
        });
        return;
      }

      // Validate password length
      if (password.length < 8) {
        res.status(400).json({
          success: false,
          message: 'Password must be at least 8 characters long',
          error: {
            type: 'VALIDATION_ERROR',
            details: {
              password: ['Password must be at least 8 characters long'],
            },
          },
        });
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        res.status(400).json({
          success: false,
          message: 'Invalid email format',
          error: {
            type: 'VALIDATION_ERROR',
            details: {
              email: ['Invalid email format'],
            },
          },
        });
        return;
      }

      // Create access operator
      const accessOperatorService = new AccessOperatorService(ServiceFactory.getPrisma());
      const accessPoint = await accessOperatorService.createAccessOperator({
        name,
        email,
        password,
        phone,
        business_name,
        address,
        geo_latitude: parseFloat(geo_latitude),
        geo_longitude: parseFloat(geo_longitude),
        approved,
      });

      // Return success response
      res.status(201).json({
        success: true,
        message: 'Access point created successfully',
        data: {
          access_point: accessPoint,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          error: {
            type: error.type,
            details: error.details,
          },
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Get all access operators (access points) with pagination, filtering, and searching
   */
  static async getAllAccessOperators(req: Request, res: Response): Promise<void> {
    try {
      // Parse pagination parameters
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;

      // Extract filtering and searching parameters
      const search = req.query.search as string | undefined;
      const approved = req.query.approved ? req.query.approved === 'true' : undefined;
      const status = req.query.status as string | undefined;

      // Get access operators with pagination, filtering, and searching
      const accessOperatorService = new AccessOperatorService(ServiceFactory.getPrisma());
      const { accessOperators, total } = await accessOperatorService.getAllAccessOperators({
        skip,
        take,
        search,
        approved,
        status,
      });

      // Return access operators with consistent pagination metadata
      res.status(200).json({
        success: true,
        message: 'Access operators retrieved successfully',
        data: {
          access_operators: accessOperators,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: (page + 1) * limit < total,
            hasPrev: page > 0
          }
        },
      });
    } catch (error) {
      if (error instanceof Error && (error.message.includes('Page number') || error.message.includes('Limit must'))) {
        res.status(400).json({
          error: 'Invalid pagination parameters',
          message: error.message
        });
        return;
      }
      console.error('Error getting access operators:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve access operators. Please try again later.'
      });
    }
  }

  /**
   * Get access operator by ID
   */
  static async getAccessOperatorById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          error: 'Invalid request',
          message: 'Access operator ID is required'
        });
        return;
      }

      // Get access operator
      const accessOperatorService = new AccessOperatorService(ServiceFactory.getPrisma());
      const accessOperator = await accessOperatorService.getAccessOperatorById(id);

      if (!accessOperator) {
        res.status(404).json({
          error: 'Not found',
          message: 'Access operator not found'
        });
        return;
      }

      // Return access operator data
      res.status(200).json({
        success: true,
        message: 'Access operator retrieved successfully',
        data: {
          access_operator: accessOperator
        }
      });
    } catch (error) {
      console.error('Error getting access operator:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve access operator. Please try again later.'
      });
    }
  }

  /**
   * Update access point (Access Operator only)
   */
  static async updateAccessPoint(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const userType = req.user?.user_type;
      const accessPointId = req.params.id;

      // Verify user is authenticated and is an access operator
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
          error: {
            type: 'AUTHENTICATION_ERROR',
            details: 'User not authenticated',
          },
        });
        return;
      }

      if (userType !== UserType.ACCESS_OPERATOR) {
        res.status(403).json({
          success: false,
          message: 'Forbidden',
          error: {
            type: 'AUTHORIZATION_ERROR',
            details: 'Only access operators can update access points',
          },
        });
        return;
      }

      // Verify the access operator is updating their own access point
      if (userId !== accessPointId) {
        res.status(403).json({
          success: false,
          message: 'Forbidden',
          error: {
            type: 'AUTHORIZATION_ERROR',
            details: 'You can only update your own access point',
          },
        });
        return;
      }

      // Extract and validate request data
      const {
        name,
        phone,
        business_name,
        address,
        geo_latitude,
        geo_longitude,
      } = req.body;

      // Validate geo coordinates if provided
      if (geo_latitude !== undefined) {
        const lat = parseFloat(geo_latitude);
        if (isNaN(lat) || lat < -90 || lat > 90) {
          res.status(400).json({
            success: false,
            message: 'Invalid latitude',
            error: {
              type: 'VALIDATION_ERROR',
              details: 'Latitude must be a number between -90 and 90',
            },
          });
          return;
        }
      }

      if (geo_longitude !== undefined) {
        const lng = parseFloat(geo_longitude);
        if (isNaN(lng) || lng < -180 || lng > 180) {
          res.status(400).json({
            success: false,
            message: 'Invalid longitude',
            error: {
              type: 'VALIDATION_ERROR',
              details: 'Longitude must be a number between -180 and 180',
            },
          });
          return;
        }
      }

      // Prepare update data
      const updateData: any = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;
      if (business_name) updateData.business_name = business_name;
      if (address) updateData.address = address;
      if (geo_latitude !== undefined) updateData.geo_latitude = parseFloat(geo_latitude);
      if (geo_longitude !== undefined) updateData.geo_longitude = parseFloat(geo_longitude);

      // Check if there's any data to update
      if (Object.keys(updateData).length === 0) {
        res.status(400).json({
          success: false,
          message: 'No data provided for update',
          error: {
            type: 'VALIDATION_ERROR',
            details: 'At least one field must be provided for update',
          },
        });
        return;
      }

      // Update access operator
      const accessOperatorService = new AccessOperatorService(ServiceFactory.getPrisma());
      const updatedAccessPoint = await accessOperatorService.updateAccessOperator(accessPointId, updateData);

      // Return success response
      res.status(200).json({
        success: true,
        message: 'Access point updated successfully',
        data: {
          access_point: updatedAccessPoint,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          error: {
            type: error.type,
            details: error.details,
          },
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Validate access point update request
   */
  static validateUpdateAccessPoint = validateRequest(AccessPointUpdateSchema);


}
