import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import { UserType } from '../types/models';

/**
 * Authentication middleware to verify JWT token
 */
export const authenticate = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No token provided' });
      return;
    }

    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');

    // Add user data to request
    req.user = decoded as jwt.JwtPayload;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

/**
 * Authorization middleware to check user type
 */
export const authorize = (allowedUserTypes: UserType[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if user type is allowed
      const userType = req.user.user_type as UserType;
      if (!allowedUserTypes.includes(userType)) {
        res.status(403).json({ error: 'Forbidden' });
        return;
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      res.status(403).json({ error: 'Forbidden' });
    }
  };
};
