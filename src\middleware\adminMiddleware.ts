import { Request, Response, NextFunction } from "express";

import { AppError } from "../utils/errors";

/**
 * Middleware to check if the authenticated user is an admin
 */
export const adminMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      throw AppError.unauthorized("Not authenticated");
    }

    // Check if user is an admin
    if (!req.user.isAdmin) {
      throw AppError.forbidden("Admin access required");
    }

    // User is an admin, proceed
    next();
  } catch (error) {
    AppError.handleError(error, req, res);
  }
};
