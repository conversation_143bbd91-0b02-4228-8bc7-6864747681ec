# Operator Access Control Implementation

## Overview

This document describes the implementation of access control for Access Operators (AO) and Car Operators (CO) to ensure they can only perform operational tasks when both **ACTIVE** and **APPROVED**.

## Requirements

- AO and CO users must be both `ACTIVE` (user.status) and `APPROVED` (operator.approved) to perform operational tasks
- Users should still be able to update their profiles even when not approved/active
- CUSTOMER and ADMIN users are not affected by operator approval checks
- Clear error messages should guide users on what actions they can take

## Implementation

### 1. New Middleware: `requireActiveAndApprovedOperator`

**File:** `src/middleware/operatorStatusMiddleware.ts`

This middleware:
- Checks if user is authenticated (relies on previous auth middleware)
- For ACCESS_OPERATOR and CAR_OPERATOR: validates both `user.status === 'ACTIVE'` AND `operator.approved === true`
- For CUSTOMER and ADMIN: only validates `user.status === 'ACTIVE'`
- Returns appropriate error messages for different failure scenarios
- Performs database queries to fetch operator approval status

### 2. Protected Routes

The middleware is applied to operational routes that require approved operators:

#### Shipment Operations
- `POST /shipments/scan` - Shipment scanning by AO/CO
- `POST /shipments/deliver` - Shipment delivery by AO

#### QR Label Operations (AO only)
- `POST /qr-labels/generate-for-shipment`
- `POST /qr-labels/generate`
- `POST /qr-labels/use-for-shipment`
- `GET /qr-labels/unused`
- `POST /qr-labels/generate-pdf`

### 3. Accessible Routes (No Additional Restrictions)

These routes remain accessible even for non-approved operators:
- `GET /users/profile` - View profile
- `PUT /users/profile` - Update profile (allows operators to complete their profiles)
- `GET /access-points` - List access points (read-only)
- `GET /access-points/:id` - View specific access point (read-only)
- Authentication routes
- Dashboard routes (basic info)

## Error Responses

### User Not Active
```json
{
  "success": false,
  "message": "Account is not active",
  "error": {
    "type": "AUTHORIZATION_ERROR",
    "details": "Your account is pending approval, suspended, or inactive. Please contact support."
  }
}
```

### Operator Not Approved
```json
{
  "success": false,
  "message": "Access Operator account pending approval",
  "error": {
    "type": "AUTHORIZATION_ERROR",
    "details": "Your Access Operator account is pending admin approval. You can update your profile but cannot perform operational tasks until approved."
  }
}
```

## Middleware Chain

The middleware is positioned in the chain as follows:
```
authenticate → userContextMiddleware → requireActiveAndApprovedOperator → authorize → controller
```

## Database Schema

### User Status (user.status)
- `ACTIVE` - User can access the system
- `PENDING` - User registration pending
- `SUSPENDED` - User temporarily suspended
- `INACTIVE` - User deactivated

### Operator Approval (operator.approved)
- `true` - Operator approved for operations
- `false` - Operator pending approval (default)

## Testing

Comprehensive tests are included in `src/tests/operatorStatusMiddleware.test.ts` covering:
- Unauthenticated users
- Invalid tokens
- Non-existent users
- Inactive users
- Non-approved operators
- Active and approved operators
- Different user types (CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR, ADMIN)

## Usage Examples

### Approved Access Operator
✅ Can scan shipments, generate QR labels, deliver packages
✅ Can update profile

### Non-Approved Access Operator
❌ Cannot scan shipments, generate QR labels, or deliver packages
✅ Can update profile to complete registration
✅ Can view access points (read-only)

### Customer
✅ Can scan shipments (for their own packages)
✅ Can update profile
✅ Not affected by operator approval checks

## Security Considerations

1. **Database Queries**: The middleware performs database queries to check approval status, which adds latency but ensures real-time accuracy
2. **Error Messages**: Clear but not overly detailed to prevent information leakage
3. **Bypass Prevention**: Middleware is applied at the route level, preventing bypass through different endpoints
4. **Profile Access**: Maintained to allow users to complete their profiles for approval

## Future Enhancements

1. **Caching**: Consider caching operator approval status to reduce database queries
2. **Audit Logging**: Log access attempts by non-approved operators
3. **Notification**: Notify operators when their approval status changes
4. **Granular Permissions**: More fine-grained permissions based on operator capabilities
