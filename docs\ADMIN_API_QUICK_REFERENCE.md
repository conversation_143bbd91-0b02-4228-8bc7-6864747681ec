# Admin API Quick Reference

## Base URL
`/api/admin`

## Authentication
All endpoints require JWT Bearer token:
```
Authorization: Bearer <jwt_token>
```

## Quick Endpoint Reference

### 🔐 Authentication
- `POST /login` - Admin login
- `GET /profile` - Get admin profile
- `PUT /profile` - Update profile
- `POST /change-password` - Change password
- `POST /logout` - Logout

### 📊 Dashboard & Analytics
- `GET /dashboard` - Main dashboard data
- `GET /analytics/overview` - System overview
- `GET /analytics/users` - User analytics
- `GET /analytics/shipments` - Shipment analytics
- `GET /analytics/performance` - Performance metrics
- `GET /system/health` - System health check

### 👥 User Management
- `GET /users` - List all users (with filters)
- `GET /users/:id` - User details
- `PUT /users/:id` - Update user
- `POST /users/:id/status` - Change user status
- `POST /users/bulk-approve` - Bulk approve operators
- `POST /users/bulk-reject` - Bulk reject operators
- `GET /users/:id/activity` - User activity history
- `POST /users/:id/notify` - Send notification to user

### 👨‍💼 Admin Management
- `GET /admins` - List all admins
- `POST /admins` - Create new admin
- `GET /admins/:id` - Admin details
- `PUT /admins/:id` - Update admin
- `POST /admins/:id/status` - Change admin status
- `DELETE /admins/:id` - Deactivate admin

### 📦 Shipment Management
- `GET /shipments` - List all shipments (with filters)
- `GET /shipments/:id` - Shipment details
- `PUT /shipments/:id` - Update shipment
- `POST /shipments/:id/status` - Change shipment status
- `POST /shipments/bulk-cancel` - Bulk cancel shipments
- `GET /shipments/expired` - Get expired shipments
- `POST /shipments/:id/reassign` - Reassign shipment

### ⚙️ System Settings
- `GET /settings` - Get system settings
- `PUT /settings` - Update system settings
- `POST /settings/reset` - Reset to defaults
- `GET /settings/history` - Settings change history

### 🔔 Notifications
- `GET /notifications` - List all notifications
- `POST /notifications/broadcast` - Send broadcast
- `GET /notifications/templates` - Get templates
- `POST /notifications/templates` - Create template
- `PUT /notifications/templates/:id` - Update template
- `GET /notifications/stats` - Notification statistics

### 🔒 Security & Audit
- `GET /audit-logs` - Get audit logs
- `GET /security/login-attempts` - Login attempts
- `GET /security/events` - Security events
- `POST /security/lock-user` - Lock user account
- `POST /security/unlock-user` - Unlock user account

### 📈 Reports & Export
- `GET /reports/users` - User reports
- `GET /reports/shipments` - Shipment reports
- `GET /reports/system` - System reports
- `POST /export/users` - Export user data
- `POST /export/shipments` - Export shipment data
- `GET /export/:id/download` - Download export

## Common Query Parameters

### Pagination
- `page`: Page number (default: 0)
- `limit`: Items per page (default: 20, max: 100)

### Filtering
- `search`: Text search
- `status`: Filter by status
- `user_type`: Filter by user type
- `sort`: Sort field (default: 'created_at')
- `order`: Sort order 'asc'/'desc' (default: 'desc')

### Date Filtering
- `date_from`: Start date (ISO format)
- `date_to`: End date (ISO format)

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { /* response data */ },
  "pagination": { /* pagination info */ }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "type": "ERROR_TYPE",
    "details": "Detailed error info"
  }
}
```

## Key Data Models

### User Object
```json
{
  "id": "uuid",
  "name": "User Name",
  "email": "<EMAIL>",
  "user_type": "CUSTOMER|ACCESS_OPERATOR|CAR_OPERATOR",
  "status": "ACTIVE|PENDING|SUSPENDED",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Shipment Object
```json
{
  "id": "uuid",
  "tracking_code": "ABC12345",
  "status": "PENDING|IN_TRANSIT|DELIVERED|CANCELLED",
  "customer_id": "uuid",
  "origin_ao_id": "uuid",
  "dest_ao_id": "uuid",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Admin Object
```json
{
  "id": "uuid",
  "name": "Admin Name",
  "email": "<EMAIL>",
  "role": "ADMIN|AUDITOR|SUPPORT",
  "status": "ACTIVE|SUSPENDED",
  "created_at": "2024-01-01T00:00:00Z"
}
```

## Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Rate Limits
- Standard endpoints: 100 req/min
- Bulk operations: 10 req/min
- Auth endpoints: 5 req/min

## Frontend Implementation Tips

### State Management
```javascript
// Example user state structure
const userState = {
  users: [],
  currentUser: null,
  loading: false,
  error: null,
  pagination: {
    page: 0,
    limit: 20,
    total: 0,
    totalPages: 0
  },
  filters: {
    search: '',
    user_type: '',
    status: ''
  }
};
```

### API Call Example
```javascript
// Example API call with error handling
const fetchUsers = async (filters = {}) => {
  try {
    setLoading(true);
    const response = await fetch('/api/admin/users?' + new URLSearchParams(filters), {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch users');
    }
    
    const data = await response.json();
    setUsers(data.data);
    setPagination(data.pagination);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

### Bulk Operations Example
```javascript
// Example bulk approve
const bulkApprove = async (userIds) => {
  try {
    const response = await fetch('/api/admin/users/bulk-approve', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ user_ids: userIds })
    });
    
    const data = await response.json();
    if (data.success) {
      // Refresh user list
      fetchUsers();
      showSuccessMessage(data.message);
    }
  } catch (error) {
    showErrorMessage(error.message);
  }
};
```

## Security Best Practices

1. **Token Management**: Store JWT securely, implement refresh logic
2. **Input Validation**: Validate all user inputs on frontend
3. **Error Handling**: Don't expose sensitive error details
4. **HTTPS Only**: Ensure all API calls use HTTPS
5. **Rate Limiting**: Implement client-side rate limiting
6. **Audit Trail**: Log important user actions

## Performance Optimization

1. **Pagination**: Always use pagination for large datasets
2. **Debouncing**: Debounce search inputs (300ms recommended)
3. **Caching**: Cache frequently accessed data
4. **Lazy Loading**: Load data on demand
5. **Virtual Scrolling**: For very large lists
6. **Optimistic Updates**: Update UI before API confirmation

## Testing Considerations

1. **Unit Tests**: Test individual components
2. **Integration Tests**: Test API integration
3. **E2E Tests**: Test complete user flows
4. **Error Scenarios**: Test error handling
5. **Performance Tests**: Test with large datasets
6. **Accessibility Tests**: Ensure WCAG compliance
