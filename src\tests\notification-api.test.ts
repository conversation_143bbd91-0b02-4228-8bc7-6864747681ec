/* eslint-disable */
/* eslint-env jest */

// @ts-nocheck
// Integration tests for Notification API routes

import request from "supertest";
import express, { Express } from "express";

import notificationRoutes from "../routes/notificationRoutes";

// ---------------------------
// Mocks
// ---------------------------

// Mock the authentication middleware so every request is treated as coming from an
// authenticated CUSTOMER with id "user-1". We keep the signature so that Express
// can still invoke it normally.
jest.mock("../middleware/auth", () => ({
  authenticate: (req: any, _res: any, next: any) => {
    req.user = {
      id: "user-1",
      user_type: "CUSTOMER",
    };
    return next();
  },
}));

// Create a reusable mock NotificationService with Jest spies for every method
const mockNotificationService = {
  getNotificationsByUserId: jest.fn(),
  getNotificationCount: jest.fn(),
  markNotificationAsRead: jest.fn(),
  markAllNotificationsAsRead: jest.fn(),
  getNotificationPreferences: jest.fn(),
  updateNotificationPreferences: jest.fn(),
};

// Mock ServiceFactory so that controllers receive our mocked NotificationService
jest.mock("../services/ServiceFactory", () => ({
  ServiceFactory: {
    // We only need the one method for these tests
    getNotificationService: () => mockNotificationService,
  },
}));

// ---------------------------
// Test Suite
// ---------------------------

describe("Notification API routes", () => {
  let app: Express;

  beforeEach(() => {
    // Reset all mock call counts before each test
    jest.clearAllMocks();

    // Build a minimal Express app that only mounts the notification routes
    app = express();
    app.use(express.json());
    app.use("/api/notifications", notificationRoutes);
  });

  // ---------------------------------
  // GET /api/notifications
  // ---------------------------------
  it("should list notifications with pagination information", async () => {
    const notifications = [
      { id: "n1", title: "Test", message: "Hello", read: false },
    ];

    // The controller calls getNotificationCount twice: once for total, once for unread
    mockNotificationService.getNotificationsByUserId.mockResolvedValueOnce(
      notifications
    );
    mockNotificationService.getNotificationCount
      .mockResolvedValueOnce(notifications.length) // totalCount
      .mockResolvedValueOnce(0); // unreadCount

    const res = await request(app).get("/api/notifications");

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.notifications).toEqual(notifications);

    // Ensure service was called with authenticated user id
    expect(
      mockNotificationService.getNotificationsByUserId
    ).toHaveBeenCalledWith("user-1", expect.any(Object), expect.any(Object));
  });

  // ---------------------------------
  // GET /api/notifications/unread-count
  // ---------------------------------
  it("should return unread notification count", async () => {
    mockNotificationService.getNotificationCount.mockResolvedValueOnce(3);

    const res = await request(app).get("/api/notifications/unread-count");

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.unreadCount).toBe(3);
    expect(mockNotificationService.getNotificationCount).toHaveBeenCalledWith(
      "user-1",
      { read: false }
    );
  });

  // ---------------------------------
  // PUT /api/notifications/:id/read
  // ---------------------------------
  it("should mark a single notification as read", async () => {
    const notificationId = "n1";
    const updatedNotification = { id: notificationId, read: true };

    // First, controller checks that the notification belongs to the user
    mockNotificationService.getNotificationsByUserId.mockResolvedValueOnce([
      { id: notificationId },
    ]);
    mockNotificationService.markNotificationAsRead.mockResolvedValueOnce(
      updatedNotification
    );

    const res = await request(app).put(
      `/api/notifications/${notificationId}/read`
    );

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(mockNotificationService.markNotificationAsRead).toHaveBeenCalledWith(
      notificationId
    );
  });

  // ---------------------------------
  // PUT /api/notifications/mark-all-read
  // ---------------------------------
  it("should mark all notifications as read for the user", async () => {
    mockNotificationService.markAllNotificationsAsRead.mockResolvedValueOnce(
      undefined
    );

    const res = await request(app).put("/api/notifications/mark-all-read");

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(
      mockNotificationService.markAllNotificationsAsRead
    ).toHaveBeenCalledWith("user-1");
  });

  // ---------------------------------
  // GET /api/notifications/preferences
  // ---------------------------------
  it("should fetch notification preferences", async () => {
    const prefs = { id: "pref-1", email_notifications: true };
    mockNotificationService.getNotificationPreferences.mockResolvedValueOnce(
      prefs
    );

    const res = await request(app).get("/api/notifications/preferences");

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.preferences).toEqual(prefs);
  });

  // ---------------------------------
  // PUT /api/notifications/preferences
  // ---------------------------------
  it("should update notification preferences", async () => {
    const updated = { id: "pref-1", email_notifications: false };
    mockNotificationService.updateNotificationPreferences.mockResolvedValueOnce(
      updated
    );

    const res = await request(app)
      .put("/api/notifications/preferences")
      .send({ email_notifications: false });

    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.preferences).toEqual(updated);
    expect(
      mockNotificationService.updateNotificationPreferences
    ).toHaveBeenCalledWith(
      "user-1",
      expect.objectContaining({ email_notifications: false })
    );
  });
});
