import { PrismaClient } from "@prisma/client";

import { BaseService } from "./BaseService";
import { ShipmentNotificationService } from "./ShipmentNotificationService";
import { IQRLabelService, IAuditLogService } from "../interfaces/services";
import { ShipmentQRLabel, QRLabelStatus } from "../types/models";

export class QRLabelService extends BaseService implements IQRLabelService {
  private auditLogService: IAuditLogService;
  private shipmentNotificationService?: ShipmentNotificationService;

  constructor(
    prisma: PrismaClient,
    auditLogService: IAuditLogService,
    shipmentNotificationService?: ShipmentNotificationService
  ) {
    super(prisma);
    this.auditLogService = auditLogService;
    this.shipmentNotificationService = shipmentNotificationService;
  }

  async generateQRLabels(
    aoId: string,
    count: number,
    updatedBy?: string
  ): Promise<ShipmentQRLabel[]> {
    // Verify the AO exists
    const accessOperator = await this.prisma.accessOperator.findUnique({
      where: { id: aoId },
    });

    if (!accessOperator) {
      throw new Error(`Access Operator with ID ${aoId} not found`);
    }

    // Check current unused QR labels count to prevent excessive generation
    const unusedCount = await this.prisma.shipmentQRLabel.count({
      where: {
        ao_id: aoId,
        status: QRLabelStatus.UNUSED,
      },
    });

    // Limit total unused QR labels to prevent abuse (max 500 unused at a time)
    if (unusedCount + count > 500) {
      throw new Error(
        `Cannot generate ${count} QR labels. You currently have ${unusedCount} unused labels. Maximum allowed unused labels is 500.`
      );
    }

    const createPromises = [];

    for (let i = 0; i < count; i++) {
      // Generate a unique QR value with retry logic
      let qrValue: string;
      let attempts = 0;
      const maxAttempts = 3;

      do {
        // Generate simple QR value for AO use - only AO prefix and random string
        const randomString = Math.random().toString(36).substring(2, 10);
        qrValue = `AO_${randomString}`;
        attempts++;

        // Check if this QR value already exists (very unlikely but good practice)
        const existingQR = await this.prisma.shipmentQRLabel.findUnique({
          where: { qr_value: qrValue },
        });

        if (!existingQR) {
          break; // QR value is unique, proceed
        }

        if (attempts >= maxAttempts) {
          throw new Error(
            "Failed to generate unique QR value after multiple attempts"
          );
        }
      } while (attempts < maxAttempts);

      // Create the QR label with updatedBy tracking
      const createPromise = this.prisma.shipmentQRLabel.create({
        data: this.addUpdatedBy(
          {
            ao_id: aoId,
            qr_value: qrValue,
            status: QRLabelStatus.UNUSED,
          },
          updatedBy
        ),
      });

      createPromises.push(createPromise);
    }

    // Execute all create operations in parallel
    const createdLabels = await Promise.all(createPromises);

    // Log the action
    await this.auditLogService.createAuditLog(
      aoId,
      "QR_LABELS_GENERATED",
      undefined,
      {
        ao_id: aoId,
        count,
        qr_ids: createdLabels.map((label) => label.id),
        unused_before: unusedCount,
        unused_after: unusedCount + count,
      }
    );

    return createdLabels as ShipmentQRLabel[];
  }

  /**
   * Generate QR label for specific shipment (on-demand generation)
   * Geo coordinates are automatically extracted from the origin AO
   */
  async generateQRLabelForShipment(
    aoId: string,
    shipmentId: string,
    photoUrl?: string,
    notes?: string,
    updatedBy?: string
  ): Promise<ShipmentQRLabel> {
    // Verify the AO exists
    const accessOperator = await this.prisma.accessOperator.findUnique({
      where: { id: aoId },
    });

    if (!accessOperator) {
      throw new Error(`Access Operator with ID ${aoId} not found`);
    }

    // Verify the shipment exists and get origin AO details
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        originAO: {
          select: {
            id: true,
            business_name: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
      },
    });

    if (!shipment) {
      throw new Error(`Shipment with ID ${shipmentId} not found`);
    }

    if (!shipment.originAO) {
      throw new Error(
        `Origin Access Operator not found for shipment ${shipmentId}`
      );
    }

    // Verify that the requesting AO is the origin AO for this shipment
    if (shipment.origin_ao_id !== aoId) {
      throw new Error(
        `Access Operator ${aoId} is not authorized to generate QR for this shipment. Only the origin AO can generate QR labels.`
      );
    }

    // Extract geo coordinates from origin AO
    const geoLatitude = shipment.originAO.geo_latitude;
    const geoLongitude = shipment.originAO.geo_longitude;

    // Generate QR value - only AO prefix and random string, no shipment ID
    const randomString = Math.random().toString(36).substring(2, 10);
    const qrValue = `AO_${randomString}`;

    // Create the QR label with updatedBy tracking
    const qrLabel = await this.prisma.shipmentQRLabel.create({
      data: this.addUpdatedBy(
        {
          ao_id: aoId,
          qr_value: qrValue,
          status: QRLabelStatus.ASSIGNED,
          shipment_id: shipmentId,
          assigned_at: new Date(),
        },
        updatedBy
      ),
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      aoId,
      "QR_LABEL_GENERATED_FOR_SHIPMENT",
      shipmentId,
      {
        ao_id: aoId,
        shipment_id: shipmentId,
        qr_value: qrValue,
        qr_label_id: qrLabel.id,
      }
    );

    // Store photo if provided (using geo coordinates from origin AO)
    if (photoUrl && updatedBy) {
      await this.prisma.shipmentPhoto.create({
        data: {
          shipment_id: shipmentId,
          user_id: updatedBy,
          photo_url: photoUrl,
          action: "QR_ASSIGNMENT" as any,
          notes,
          latitude: geoLatitude,
          longitude: geoLongitude,
        },
      });
    }

    // Send QR assignment notifications
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(shipmentId);
        if (context) {
          await this.shipmentNotificationService.notifyQRAssigned(context);
        }
      }
    } catch (error) {
      console.error("❌ Failed to send QR assignment notifications:", error);
      // Don't throw error to avoid breaking QR assignment
    }

    return qrLabel as ShipmentQRLabel;
  }

  /**
   * Use any unused QR label for a shipment (AO can pick any unused QR)
   */
  async useUnusedQRLabelForShipment(
    aoId: string,
    shipmentId: string,
    updatedBy?: string
  ): Promise<ShipmentQRLabel> {
    // Verify the AO exists
    const accessOperator = await this.prisma.accessOperator.findUnique({
      where: { id: aoId },
    });

    if (!accessOperator) {
      throw new Error(`Access Operator with ID ${aoId} not found`);
    }

    // Verify the shipment exists
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
    });

    if (!shipment) {
      throw new Error(`Shipment with ID ${shipmentId} not found`);
    }

    // Find any unused QR label for this AO
    const unusedQRLabel = await this.prisma.shipmentQRLabel.findFirst({
      where: {
        ao_id: aoId,
        status: QRLabelStatus.UNUSED,
      },
      orderBy: { created_at: "asc" }, // Use oldest unused QR first
    });

    if (!unusedQRLabel) {
      throw new Error(
        `No unused QR labels available for Access Operator ${aoId}`
      );
    }

    // Assign the unused QR label to the shipment
    const updatedQRLabel = await this.prisma.shipmentQRLabel.update({
      where: { id: unusedQRLabel.id },
      data: this.addUpdatedBy(
        {
          shipment_id: shipmentId,
          status: QRLabelStatus.ASSIGNED,
          assigned_at: new Date(),
        },
        updatedBy
      ),
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      aoId,
      "QR_LABEL_USED_FOR_SHIPMENT",
      shipmentId,
      {
        ao_id: aoId,
        shipment_id: shipmentId,
        qr_value: unusedQRLabel.qr_value,
        qr_label_id: unusedQRLabel.id,
      }
    );

    // Send QR assignment notifications
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(shipmentId);
        if (context) {
          await this.shipmentNotificationService.notifyQRAssigned(context);
        }
      }
    } catch (error) {
      console.error("❌ Failed to send QR assignment notifications:", error);
      // Don't throw error to avoid breaking QR assignment
    }

    return updatedQRLabel as ShipmentQRLabel;
  }

  async getQRLabelById(id: string): Promise<ShipmentQRLabel | null> {
    const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
      where: { id },
    });

    return qrLabel as ShipmentQRLabel | null;
  }

  async getQRLabelByValue(qrValue: string): Promise<ShipmentQRLabel | null> {
    const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
      where: { qr_value: qrValue },
    });

    return qrLabel as ShipmentQRLabel | null;
  }

  async assignQRLabelToShipment(
    qrLabelId: string,
    shipmentId: string,
    updatedBy?: string
  ): Promise<ShipmentQRLabel> {
    // Verify the QR label exists and is unused
    const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
      where: { id: qrLabelId },
    });

    if (!qrLabel) {
      throw new Error(`QR Label with ID ${qrLabelId} not found`);
    }

    if (qrLabel.status !== QRLabelStatus.UNUSED) {
      throw new Error(
        `QR Label with ID ${qrLabelId} is already ${qrLabel.status}`
      );
    }

    // Verify the shipment exists
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
    });

    if (!shipment) {
      throw new Error(`Shipment with ID ${shipmentId} not found`);
    }

    // Assign the QR label to the shipment with updatedBy tracking
    const updatedQRLabel = await this.prisma.shipmentQRLabel.update({
      where: { id: qrLabelId },
      data: this.addUpdatedBy(
        {
          shipment_id: shipmentId,
          status: QRLabelStatus.ASSIGNED,
          assigned_at: new Date(),
        },
        updatedBy
      ),
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      qrLabel.ao_id,
      "QR_LABEL_ASSIGNED",
      shipmentId,
      {
        qr_label_id: qrLabelId,
        qr_value: qrLabel.qr_value,
      }
    );

    // Send QR assignment notifications
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(shipmentId);
        if (context) {
          await this.shipmentNotificationService.notifyQRAssigned(context);
        }
      }
    } catch (error) {
      console.error("❌ Failed to send QR assignment notifications:", error);
      // Don't throw error to avoid breaking QR assignment
    }

    return updatedQRLabel as ShipmentQRLabel;
  }

  async markQRLabelAsUsed(
    id: string,
    updatedBy?: string
  ): Promise<ShipmentQRLabel> {
    // Verify the QR label exists and is assigned
    const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
      where: { id },
      include: { shipment: true },
    });

    if (!qrLabel) {
      throw new Error(`QR Label with ID ${id} not found`);
    }

    if (qrLabel.status !== QRLabelStatus.ASSIGNED) {
      throw new Error(`QR Label with ID ${id} is not assigned`);
    }

    // Mark the QR label as used with updatedBy tracking
    const updatedQRLabel = await this.prisma.shipmentQRLabel.update({
      where: { id },
      data: this.addUpdatedBy(
        {
          status: QRLabelStatus.USED,
        },
        updatedBy
      ),
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      qrLabel.ao_id,
      "QR_LABEL_USED",
      qrLabel.shipment_id!,
      { qr_label_id: id, qr_value: qrLabel.qr_value }
    );

    return updatedQRLabel as ShipmentQRLabel;
  }

  async getUnusedQRLabels(
    aoId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ qrLabels: ShipmentQRLabel[]; total: number }> {
    const { skip, take } = options || {};

    const where = {
      ao_id: aoId,
      status: QRLabelStatus.UNUSED,
    };

    // Get total count
    const total = await this.prisma.shipmentQRLabel.count({ where });

    // Get QR labels with pagination
    const qrLabels = await this.prisma.shipmentQRLabel.findMany({
      where,
      skip,
      take,
    });

    return { qrLabels: qrLabels as ShipmentQRLabel[], total };
  }
}
