import { PrismaClient } from "@prisma/client";

import { AdminService } from "./AdminService";
import { AuditLogService } from "./AuditLogService";
import { EmailService } from "./EmailService";
import { NotificationService } from "./NotificationService";
import { QRLabelService } from "./QRLabelService";
import { ScheduledJobService } from "./ScheduledJobService";
import { ShipmentNotificationService } from "./ShipmentNotificationService";
import { ShipmentService } from "./ShipmentService";
import { ShipmentStatusService } from "./ShipmentStatusService";
import { UserService } from "./UserService";

/**
 * Factory class for creating service instances with proper dependency injection
 */
export class ServiceFactory {
  private static prisma: PrismaClient;

  // Singleton instances
  private static auditLogService: AuditLogService;
  private static notificationService: NotificationService;
  private static shipmentNotificationService: ShipmentNotificationService;
  private static emailService: EmailService;
  private static userService: UserService;
  private static adminService: AdminService;
  private static shipmentService: ShipmentService;
  private static qrLabelService: QRLabelService;
  private static shipmentStatusService: ShipmentStatusService;
  private static scheduledJobService: ScheduledJobService;

  // Initialize the factory with a Prisma client
  public static initialize(prisma: PrismaClient): void {
    this.prisma = prisma;

    // Create services in the correct order based on dependencies
    this.auditLogService = new AuditLogService(this.prisma);
    this.emailService = new EmailService(this.prisma);
    this.notificationService = new NotificationService(this.prisma);

    // Create ShipmentNotificationService
    this.shipmentNotificationService = new ShipmentNotificationService(
      this.prisma,
      this.notificationService,
      this.emailService
    );

    // Create ShipmentStatusService before other services that depend on it
    this.shipmentStatusService = new ShipmentStatusService(
      this.prisma,
      this.auditLogService,
      this.notificationService,
      this.shipmentNotificationService
    );

    this.userService = new UserService(
      this.prisma,
      this.auditLogService,
      this.emailService
    );
    this.adminService = new AdminService(
      this.prisma,
      this.emailService,
      this.auditLogService
    );
    this.shipmentService = new ShipmentService(
      this.prisma,
      this.auditLogService,
      this.notificationService,
      this.shipmentStatusService,
      this.shipmentNotificationService
    );
    this.qrLabelService = new QRLabelService(
      this.prisma,
      this.auditLogService,
      this.shipmentNotificationService
    );

    // Initialize ScheduledJobService
    this.scheduledJobService = new ScheduledJobService(this.prisma);

    // Initialize other services as needed
  }

  // Getters for service instances
  public static getAuditLogService(): AuditLogService {
    return this.auditLogService;
  }

  public static getNotificationService(): NotificationService {
    return this.notificationService;
  }

  public static getShipmentNotificationService(): ShipmentNotificationService {
    return this.shipmentNotificationService;
  }

  public static getEmailService(): EmailService {
    return this.emailService;
  }

  public static getUserService(): UserService {
    return this.userService;
  }

  public static getAdminService(): AdminService {
    return this.adminService;
  }

  public static getShipmentService(): ShipmentService {
    return this.shipmentService;
  }

  public static getQRLabelService(): QRLabelService {
    return this.qrLabelService;
  }

  public static getShipmentStatusService(): ShipmentStatusService {
    return this.shipmentStatusService;
  }

  public static getScheduledJobService(): ScheduledJobService {
    return this.scheduledJobService;
  }

  // Add getters for other services as needed

  public static getPrisma(): PrismaClient {
    return this.prisma;
  }

  public static getPrismaClient(): PrismaClient {
    return this.prisma;
  }
}
