{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "types": ["node", "jest"], "rootDir": "./src", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "sourceMap": true}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}