# Admin Dashboard API Documentation

## Overview
Complete API documentation for the Admin Dashboard frontend development. This covers all endpoints, request/response formats, authentication, and data models.

## Base Configuration
- **Base URL**: `http://localhost:8000/api/admin`
- **Content-Type**: `application/json`
- **Authentication**: JW<PERSON> (8-hour expiry)

## Authentication Flow

### 1. Admin Login
**Endpoint**: `POST /api/admin/login`

**Request**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "uuid",
      "name": "Admin Name",
      "email": "<EMAIL>",
      "role": "ADMIN",
      "status": "ACTIVE",
      "created_at": "2025-01-01T00:00:00.000Z",
      "updated_at": "2025-01-01T00:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Response** (Error - 401):
```json
{
  "error": "Invalid credentials",
  "message": "Email or password is incorrect"
}
```

### 2. Admin Registration
**Endpoint**: `POST /api/admin/register`

**Request**:
```json
{
  "name": "New Admin",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "ADMIN"
}
```

**Response** (Success - 201):
```json
{
  "success": true,
  "message": "Admin registered successfully. Please verify your email with the OTP sent.",
  "data": {
    "admin": {
      "id": "uuid",
      "name": "New Admin",
      "email": "<EMAIL>",
      "role": "ADMIN",
      "status": "PENDING"
    }
  }
}
```

### 3. OTP Verification
**Endpoint**: `POST /api/admin/verify-otp`

**Request**:
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Admin account verified successfully",
  "data": {
    "admin": {
      "id": "uuid",
      "status": "ACTIVE"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 4. Forgot Password
**Endpoint**: `POST /api/admin/forgot-password`

**Request**:
```json
{
  "email": "<EMAIL>"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Password reset link sent to your email"
}
```

### 5. Reset Password
**Endpoint**: `POST /api/admin/reset-password`

**Request**:
```json
{
  "token": "reset-token-from-email",
  "email": "<EMAIL>",
  "newPassword": "newpassword123"
}
```

## Dashboard & Statistics

### 1. Dashboard Overview
**Endpoint**: `GET /api/dashboard`
**Auth Required**: Yes

**Headers**:
```
Authorization: Bearer <jwt_token>
```

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "user_stats": {
      "total": 1250,
      "customers": 800,
      "access_operators": 300,
      "car_operators": 150,
      "pending_approvals": 25,
      "active_users": 1200,
      "inactive_users": 50
    },
    "shipment_stats": {
      "total": 5000,
      "active": 150,
      "delivered": 4500,
      "cancelled": 300,
      "expired": 50,
      "delivery_rate": 90.5,
      "pending": 75,
      "in_transit": 75
    },
    "recent_activities": [
      {
        "id": "uuid",
        "type": "USER_REGISTERED",
        "description": "New customer registered",
        "timestamp": "2025-01-01T10:00:00.000Z",
        "user_name": "John Doe"
      }
    ],
    "quick_actions": [
      {
        "title": "Manage Users",
        "description": "View and manage all users",
        "action": "navigate_to_users",
        "count": 1250
      },
      {
        "title": "Approve Operators",
        "description": "Review pending operator applications",
        "action": "navigate_to_approvals",
        "count": 25
      }
    ]
  }
}
```

### 2. Expired Shipment Statistics
**Endpoint**: `GET /api/admin/shipments/expired-stats`
**Auth Required**: Yes

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "expired_shipments": {
      "total": 50,
      "last_24h": 5,
      "last_week": 15,
      "last_month": 35
    },
    "expiry_reasons": [
      {
        "reason": "No car operator assigned",
        "count": 20
      },
      {
        "reason": "Pickup timeout",
        "count": 15
      }
    ]
  }
}
```

## User Management

### 1. Get All Users
**Endpoint**: `GET /api/admin/users`
**Auth Required**: Yes

**Query Parameters**:
- `page` (optional): Page number (default: 0)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search by name or email
- `user_type` (optional): CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR
- `status` (optional): ACTIVE, INACTIVE, PENDING
- `approval_status` (optional): APPROVED, PENDING, REJECTED

**Example Request**:
```
GET /api/admin/users?page=0&limit=20&search=john&user_type=CUSTOMER&status=ACTIVE
```

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "uuid",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+**********",
        "user_type": "CUSTOMER",
        "status": "ACTIVE",
        "approval_status": "APPROVED",
        "created_at": "2025-01-01T00:00:00.000Z",
        "updated_at": "2025-01-01T00:00:00.000Z",
        "last_login": "2025-01-01T10:00:00.000Z"
      }
    ],
    "total": 1250,
    "pagination": {
      "page": 0,
      "limit": 20,
      "total": 1250,
      "totalPages": 63,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. Get User by ID
**Endpoint**: `GET /api/admin/users/:id`
**Auth Required**: Yes

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "user_type": "ACCESS_OPERATOR",
      "status": "ACTIVE",
      "approval_status": "APPROVED",
      "created_at": "2025-01-01T00:00:00.000Z",
      "updated_at": "2025-01-01T00:00:00.000Z",
      "last_login": "2025-01-01T10:00:00.000Z",
      "profile": {
        "business_name": "Quick Shop",
        "business_address": "123 Main St",
        "business_license": "LIC123456",
        "operating_hours": "9AM-9PM"
      },
      "statistics": {
        "total_shipments": 150,
        "completed_shipments": 140,
        "success_rate": 93.3
      }
    }
  }
}
```

### 3. Change User Status
**Endpoint**: `POST /api/admin/users/status`
**Auth Required**: Yes

**Request**:
```json
{
  "userId": "user-uuid",
  "status": "INACTIVE",
  "reason": "Violation of terms"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "User status updated successfully",
  "data": {
    "user": {
      "id": "user-uuid",
      "status": "INACTIVE",
      "updated_at": "2025-01-01T10:00:00.000Z"
    }
  }
}
```

### 4. Change Operator Approval
**Endpoint**: `POST /api/admin/users/approval`
**Auth Required**: Yes

**Request** (Option 1 - String approval_status):
```json
{
  "userId": "operator-uuid",
  "approval_status": "APPROVED"
}
```

**Request** (Option 2 - Boolean approved):
```json
{
  "userId": "operator-uuid",
  "approved": true
}
```

**Note**: `approval_status` accepts "APPROVED" or "REJECTED". `approved` accepts true or false.

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Operator approval status updated successfully",
  "data": {
    "user": {
      "id": "operator-uuid",
      "approval_status": "APPROVED",
      "updated_at": "2025-01-01T10:00:00.000Z"
    }
  }
}
```

## Admin Management

### 1. Get All Admins
**Endpoint**: `GET /api/admin/all`
**Auth Required**: Yes

**Query Parameters**:
- `page` (optional): Page number (default: 0)
- `limit` (optional): Items per page (default: 10)

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "admins": [
      {
        "id": "uuid",
        "name": "Super Admin",
        "email": "<EMAIL>",
        "role": "SUPER_ADMIN",
        "status": "ACTIVE",
        "created_at": "2025-01-01T00:00:00.000Z",
        "last_login": "2025-01-01T09:00:00.000Z"
      }
    ],
    "total": 5,
    "pagination": {
      "page": 0,
      "limit": 10,
      "total": 5,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 2. Change Admin Status
**Endpoint**: `POST /api/admin/status`
**Auth Required**: Yes (SUPER_ADMIN only)

**Request**:
```json
{
  "adminId": "admin-uuid",
  "status": "INACTIVE"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Admin status updated successfully"
}
```

## Profile Management

### 1. Get Admin Profile
**Endpoint**: `GET /api/admin/profile`
**Auth Required**: Yes

**Response** (Success - 200):
```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid",
      "name": "Admin Name",
      "email": "<EMAIL>",
      "role": "ADMIN",
      "status": "ACTIVE",
      "created_at": "2025-01-01T00:00:00.000Z",
      "updated_at": "2025-01-01T00:00:00.000Z"
    }
  }
}
```

### 2. Update Admin Profile
**Endpoint**: `PUT /api/admin/profile`
**Auth Required**: Yes

**Request**:
```json
{
  "name": "Updated Admin Name"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "admin": {
      "id": "uuid",
      "name": "Updated Admin Name",
      "email": "<EMAIL>",
      "updated_at": "2025-01-01T10:00:00.000Z"
    }
  }
}
```

### 3. Change Password
**Endpoint**: `POST /api/admin/change-password`
**Auth Required**: Yes

**Request**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

**Response** (Success - 200):
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

## Data Models & Enums

### User Types
```typescript
enum UserType {
  CUSTOMER = "CUSTOMER",
  ACCESS_OPERATOR = "ACCESS_OPERATOR", 
  CAR_OPERATOR = "CAR_OPERATOR"
}
```

### User Status
```typescript
enum UserStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  PENDING = "PENDING"
}
```

### Approval Status
```typescript
enum ApprovalStatus {
  APPROVED = "APPROVED",
  PENDING = "PENDING",
  REJECTED = "REJECTED"
}
```

### Admin Roles
```typescript
enum AdminRole {
  ADMIN = "ADMIN",
  SUPER_ADMIN = "SUPER_ADMIN"
}
```

### Shipment Status
```typescript
enum ShipmentStatus {
  PENDING = "PENDING",
  ASSIGNED = "ASSIGNED",
  IN_TRANSIT = "IN_TRANSIT",
  DELIVERED = "DELIVERED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED"
}
```

## Error Handling

### Common Error Responses

**401 Unauthorized**:
```json
{
  "error": "Unauthorized",
  "message": "Authentication required"
}
```

**403 Forbidden**:
```json
{
  "error": "Forbidden", 
  "message": "Insufficient permissions"
}
```

**404 Not Found**:
```json
{
  "error": "Not Found",
  "message": "Resource not found"
}
```

**400 Bad Request**:
```json
{
  "error": "Validation Error",
  "message": "Invalid input data",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

**500 Internal Server Error**:
```json
{
  "error": "Internal Server Error",
  "message": "Something went wrong"
}
```

## Frontend Implementation Notes

### Authentication Setup
1. Store JWT token in localStorage/sessionStorage
2. Add token to all API requests in Authorization header
3. Handle token expiration (8 hours) with refresh or re-login
4. Redirect to login on 401 responses

### Recommended State Management
```javascript
// User authentication state
const authState = {
  isAuthenticated: false,
  admin: null,
  token: null,
  loading: false,
  error: null
}

// Dashboard data state  
const dashboardState = {
  userStats: null,
  shipmentStats: null,
  recentActivities: [],
  loading: false,
  error: null
}
```

### API Client Example
```javascript
const apiClient = axios.create({
  baseURL: 'http://localhost:8000/api/admin',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('adminToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Pagination Helper
```javascript
const paginationDefaults = {
  page: 0,
  limit: 20,
  maxLimit: 100
};

const buildPaginationQuery = (page, limit) => {
  return `page=${page}&limit=${Math.min(limit, paginationDefaults.maxLimit)}`;
};
```

## Testing Endpoints

Use these curl commands to test the API:

```bash
# Login
curl -X POST http://localhost:8000/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get dashboard (replace TOKEN)
curl -X GET http://localhost:8000/api/dashboard \
  -H "Authorization: Bearer TOKEN"

# Get users with filters
curl -X GET "http://localhost:8000/api/admin/users?page=0&limit=10&user_type=CUSTOMER" \
  -H "Authorization: Bearer TOKEN"
```

## Development Checklist

### Phase 1: Authentication
- [ ] Login page with email/password
- [ ] OTP verification page
- [ ] Password reset flow
- [ ] Token management and auto-logout

### Phase 2: Dashboard
- [ ] Overview statistics display
- [ ] Recent activities feed
- [ ] Quick action buttons
- [ ] Responsive layout

### Phase 3: User Management
- [ ] User list with search/filters
- [ ] User detail view
- [ ] Status change functionality
- [ ] Operator approval workflow

### Phase 4: Admin Management
- [ ] Admin list and management
- [ ] Profile settings
- [ ] Password change
- [ ] Role-based permissions

### Phase 5: Advanced Features
- [ ] Real-time notifications
- [ ] Export functionality
- [ ] Advanced analytics
- [ ] Audit logs