/* eslint-disable no-unused-vars */
import { Admin, AdminRole, UserStatus } from '@prisma/client';

export interface IAdminService {
  /**
   * Register a new admin
   */
  registerAdmin(
    name: string,
    email: string,
    password: string,
    role?: AdminRole
  ): Promise<Admin>;

  /**
   * Login admin
   */
  loginAdmin(
    email: string,
    password: string,
    ipAddress: string
  ): Promise<{ admin: Admin; token: string }>;

  /**
   * Get admin by ID
   */
  getAdminById(id: string): Promise<Admin | null>;

  /**
   * Get admin by email
   */
  getAdminByEmail(email: string): Promise<Admin | null>;

  /**
   * Update admin
   */
  updateAdmin(
    id: string,
    adminData: { name?: string; role?: AdminRole }
  ): Promise<Admin>;

  /**
   * Change admin password
   */
  changePassword(
    adminId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void>;

  /**
   * Forgot password - send reset token
   */
  forgotPassword(email: string): Promise<void>;

  /**
   * Reset password with token
   */
  resetPassword(
    email: string,
    token: string,
    newPassword: string
  ): Promise<void>;

  /**
   * Get all admins with pagination
   */
  getAllAdmins(options?: {
    skip?: number;
    take?: number;
    search?: string;
    role?: AdminRole;
    status?: UserStatus;
  }): Promise<{ admins: Admin[]; total: number }>;

  /**
   * Change admin status (activate/deactivate)
   */
  changeAdminStatus(id: string, status: UserStatus): Promise<Admin>;

  /**
   * Verify admin OTP
   */
  verifyAdminOTP(email: string, otp: string): Promise<Admin>;

  /**
   * Resend admin OTP
   */
  resendAdminOTP(email: string): Promise<void>;

  /**
   * Logout admin
   */
  logoutAdmin(adminId: string): Promise<void>;

  /**
   * Change user status (activate/deactivate)
   */
  changeUserStatus(userId: string, status: UserStatus, adminId?: string): Promise<any>;
}
