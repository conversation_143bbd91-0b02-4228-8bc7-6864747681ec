 
import { Request, Response, NextFunction } from 'express';

/**
 * Interface for user context that will be available throughout the request lifecycle
 */
export interface UserContext {
  userId: string;
  userType: string;
  email: string;
  isAdmin?: boolean;
}

/**
 * Extend Express Request interface to include userContext
 */
declare global {
  namespace Express {
    interface Request {
      userContext?: UserContext;
    }
  }
}

/**
 * Middleware to extract and standardize user context from JWT token
 * This should be used after authentication middleware
 */
export const userContextMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      // If no user, continue without setting context
      // This allows for endpoints that don't require authentication
      next();
      return;
    }

    // Extract user information from JWT payload
    const { id, user_type, email, isAdmin } = req.user;

    if (!id || !user_type || !email) {
      res.status(401).json({
        success: false,
        message: 'Invalid token payload',
        error: {
          type: 'AUTHENTICATION_ERROR',
          details: 'Token missing required user information',
        },
      });
      return;
    }

    // Set user context
    req.userContext = {
      userId: id,
      userType: user_type,
      email,
      isAdmin: isAdmin || false,
    };

    next();
  } catch (error) {
    console.error('Error in userContextMiddleware:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: {
        type: 'INTERNAL_ERROR',
        details: 'Failed to process user context',
      },
    });
  }
};

/**
 * Helper function to get user ID from request context
 * @param req - Express request object
 * @returns User ID if available, undefined otherwise
 */
export const getUserId = (req: Request): string | undefined => {
  return req.userContext?.userId;
};

/**
 * Helper function to get user type from request context
 * @param req - Express request object
 * @returns User type if available, undefined otherwise
 */
export const getUserType = (req: Request): string | undefined => {
  return req.userContext?.userType;
};

/**
 * Helper function to check if user is admin
 * @param req - Express request object
 * @returns True if user is admin, false otherwise
 */
export const isAdmin = (req: Request): boolean => {
  return req.userContext?.isAdmin || false;
};

/**
 * Helper function to get full user context
 * @param req - Express request object
 * @returns User context if available, undefined otherwise
 */
export const getUserContext = (req: Request): UserContext | undefined => {
  return req.userContext;
};

/**
 * Middleware to require user context (authenticated user)
 * Use this for endpoints that require authentication
 */
export const requireUserContext = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.userContext) {
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: {
        type: 'AUTHENTICATION_ERROR',
        details: 'User context not found',
      },
    });
    return;
  }

  next();
};
