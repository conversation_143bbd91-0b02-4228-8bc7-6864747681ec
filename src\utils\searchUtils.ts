/**
 * Search utilities for building Prisma search conditions
 * This helps prevent issues with UUID fields and ensures proper search functionality
 */

/**
 * Check if a string is a valid UUID
 */
export function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * Check if a string is a valid date
 */
export function isValidDate(str: string): Date | null {
  const date = new Date(str);
  return !isNaN(date.getTime()) ? date : null;
}

/**
 * Check if a string is a valid number
 */
export function isValidNumber(str: string): number | null {
  const num = parseFloat(str);
  return !isNaN(num) ? num : null;
}

/**
 * Build search conditions for shipment fields
 * This function properly handles different field types to prevent Prisma validation errors
 */
export function buildShipmentSearchConditions(search: string) {
  if (!search || search.trim() === '') {
    return undefined; // No search conditions if search is empty
  }

  const trimmedSearch = search.trim();
  const isSearchUUID = isValidUUID(trimmedSearch);
  const searchDate = isValidDate(trimmedSearch);
  const searchFloat = isValidNumber(trimmedSearch);

  // Check if search term matches valid enum values
  const validStatuses = ['PENDING', 'AWAITING_PICKUP', 'PICKED_UP_BY_CO', 'IN_TRANSIT', 'ARRIVED_AT_DESTINATION', 'READY_FOR_DELIVERY', 'DELIVERED', 'CANCELLED'];
  const validCancellationReasons = ['CUSTOMER_REQUEST', 'ITEM_NOT_AVAILABLE', 'ADDRESS_ISSUE', 'PAYMENT_ISSUE', 'OTHER'];
  
  const isValidStatus = validStatuses.includes(trimmedSearch.toUpperCase());
  const isValidCancellationReason = validCancellationReasons.includes(trimmedSearch.toUpperCase());

  const conditions = [];

  // UUID fields - only search if the search term is a valid UUID
  if (isSearchUUID) {
    conditions.push(
      { id: { equals: trimmedSearch } },
      { customer_id: { equals: trimmedSearch } },
      { origin_ao_id: { equals: trimmedSearch } },
      { dest_ao_id: { equals: trimmedSearch } },
      { assigned_car_operator_id: { equals: trimmedSearch } },
      { updated_by: { equals: trimmedSearch } }
    );
  }

  // Enum fields
  if (isValidStatus) {
    conditions.push({ status: { equals: trimmedSearch.toUpperCase() } });
  }
  if (isValidCancellationReason) {
    conditions.push({ cancellation_reason: { equals: trimmedSearch.toUpperCase() } });
  }

  // Numeric fields
  if (searchFloat !== null) {
    conditions.push({ weight: { equals: searchFloat } });
  }

  // Text fields - always searchable with contains (case-insensitive)
  conditions.push(
    { size: { contains: trimmedSearch, mode: "insensitive" as const } },
    { description: { contains: trimmedSearch, mode: "insensitive" as const } },
    { pickup_code: { contains: trimmedSearch, mode: "insensitive" as const } },
    { tracking_code: { contains: trimmedSearch, mode: "insensitive" as const } },
    { receiver_name: { contains: trimmedSearch, mode: "insensitive" as const } },
    { receiver_phone: { contains: trimmedSearch, mode: "insensitive" as const } }
  );

  // Date fields - only search if the search term is a valid date
  if (searchDate) {
    conditions.push(
      { estimated_delivery: { equals: searchDate } },
      { picked_up_at: { equals: searchDate } },
      { cancelled_at: { equals: searchDate } },
      { expires_at: { equals: searchDate } },
      { created_at: { equals: searchDate } },
      { updated_at: { equals: searchDate } }
    );
  }

  return {
    OR: conditions.filter(Boolean)
  };
}

/**
 * Build search conditions for user fields
 */
export function buildUserSearchConditions(search: string) {
  if (!search || search.trim() === '') {
    return undefined;
  }

  const trimmedSearch = search.trim();
  const isSearchUUID = isValidUUID(trimmedSearch);

  const conditions = [];

  // UUID fields
  if (isSearchUUID) {
    conditions.push(
      { id: { equals: trimmedSearch } },
      { updated_by: { equals: trimmedSearch } }
    );
  }

  // Text fields
  conditions.push(
    { name: { contains: trimmedSearch, mode: "insensitive" as const } },
    { email: { contains: trimmedSearch, mode: "insensitive" as const } },
    { phone: { contains: trimmedSearch, mode: "insensitive" as const } },
    { business_name: { contains: trimmedSearch, mode: "insensitive" as const } }
  );

  return {
    OR: conditions.filter(Boolean)
  };
}

/**
 * Validate search parameters to prevent common issues
 */
export function validateSearchParams(params: {
  search?: string;
  skip?: number;
  take?: number;
  sortBy?: string;
  sortOrder?: string;
}) {
  const errors: string[] = [];

  // Validate skip
  if (params.skip !== undefined) {
    if (typeof params.skip !== 'number' || params.skip < 0) {
      errors.push('skip must be a non-negative number');
    }
  }

  // Validate take
  if (params.take !== undefined) {
    if (typeof params.take !== 'number' || params.take < 1 || params.take > 100) {
      errors.push('take must be a number between 1 and 100');
    }
  }

  // Validate sortOrder
  if (params.sortOrder !== undefined) {
    if (!['asc', 'desc'].includes(params.sortOrder.toLowerCase())) {
      errors.push('sortOrder must be either "asc" or "desc"');
    }
  }

  // Validate search length
  if (params.search !== undefined && params.search.length > 255) {
    errors.push('search term cannot exceed 255 characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize search input to prevent injection attacks
 */
export function sanitizeSearchInput(search: string): string {
  if (!search) return '';
  
  // Remove potentially dangerous characters while preserving normal search functionality
  return search
    .trim()
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/['"]/g, '') // Remove quotes that could break queries
    .substring(0, 255); // Limit length
}

/**
 * Build pagination parameters with validation
 */
export function buildPaginationParams(skip?: number, take?: number) {
  const validatedSkip = Math.max(0, skip || 0);
  const validatedTake = Math.min(100, Math.max(1, take || 10));

  return {
    skip: validatedSkip,
    take: validatedTake
  };
}

/**
 * Build sort parameters with validation
 */
export function buildSortParams(sortBy?: string, sortOrder?: string, allowedFields: string[] = []) {
  const validSortOrder = ['asc', 'desc'].includes(sortOrder?.toLowerCase() || '')
    ? sortOrder?.toLowerCase() as 'asc' | 'desc'
    : 'desc';

  const validSortBy: string = allowedFields.includes(sortBy || '') ? sortBy! : 'updated_at';

  return {
    [validSortBy]: validSortOrder
  } as Record<string, 'asc' | 'desc'>;
}
