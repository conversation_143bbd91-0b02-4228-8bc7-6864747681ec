import { <PERSON><PERSON><PERSON> } from 'buffer';

import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

import { S3_CONFIG } from '../data/constant';

export class S3Service {
  private s3Client: S3Client;
  private bucketName: string;

  constructor() {
    // Initialize S3 client with credentials from environment variables
    this.s3Client = new S3Client({
      region: S3_CONFIG.region,
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY!,
        secretAccessKey: process.env.S3_SECRET_KEY!,
      },
    });
    this.bucketName = S3_CONFIG.bucketName;
  }

  /**
   * Upload a photo to S3 from base64 data
   * @param photoBase64 - Base64 encoded photo data
   * @param folder - Folder type for organizing photos
   * @returns Promise<string> - The public URL of the uploaded photo
   */
  async uploadPhotoFromBase64(
    photoBase64: string,
    folder: keyof typeof S3_CONFIG.folders = 'general'
  ): Promise<string> {
    try {
      // Remove data URL prefix if present (data:image/jpeg;base64,)
      const base64Data = photoBase64.replace(/^data:image\/[a-z]+;base64,/, '');
      
      // Decode base64 to binary
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      // Check file size (5MB max)
      const maxSizeInBytes = 5 * 1024 * 1024;
      if (imageBuffer.length > maxSizeInBytes) {
        throw new Error('Image size must be less than 5MB');
      }
      
      // Generate unique filename
      const fileName = `${uuidv4()}.jpg`;
      
      // Get the folder path
      const folderPath = S3_CONFIG.folders[folder];
      const key = `${folderPath}/${fileName}`;
      
      // Determine content type from original base64 data
      let contentType = 'image/jpeg'; // default
      if (photoBase64.startsWith('data:image/png')) {
        contentType = 'image/png';
      } else if (photoBase64.startsWith('data:image/gif')) {
        contentType = 'image/gif';
      } else if (photoBase64.startsWith('data:image/webp')) {
        contentType = 'image/webp';
      }
      
      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: imageBuffer,
        ContentType: contentType,
        // Note: ACL removed - bucket should have public read policy or use CloudFront
      });
      
      await this.s3Client.send(command);
      
      // Return the public URL
      return `https://${this.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${key}`;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload photo to S3: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload a photo buffer to S3
   * @param imageBuffer - Image buffer data
   * @param fileName - File name with extension
   * @param folder - Folder type for organizing photos
   * @param contentType - MIME type of the image
   * @returns Promise<string> - The public URL of the uploaded photo
   */
  async uploadPhotoBuffer(
    imageBuffer: Buffer,
    fileName: string,
    folder: keyof typeof S3_CONFIG.folders = 'general',
    contentType: string = 'image/jpeg'
  ): Promise<string> {
    try {
      // Check file size (5MB max)
      const maxSizeInBytes = 5 * 1024 * 1024;
      if (imageBuffer.length > maxSizeInBytes) {
        throw new Error('Image size must be less than 5MB');
      }
      
      // Get the folder path
      const folderPath = S3_CONFIG.folders[folder];
      const key = `${folderPath}/${fileName}`;
      
      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: imageBuffer,
        ContentType: contentType,
        // Note: ACL removed - bucket should have public read policy or use CloudFront
      });
      
      await this.s3Client.send(command);
      
      // Return the public URL
      return `https://${this.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${key}`;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload photo to S3: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a photo from S3
   * @param photoUrl - The full URL of the photo to delete
   * @returns Promise<void>
   */
  async deletePhoto(photoUrl: string): Promise<void> {
    try {
      // Extract the key from the URL
      const urlParts = photoUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part.includes(this.bucketName));
      if (bucketIndex === -1) {
        throw new Error('Invalid S3 URL format');
      }
      
      const key = urlParts.slice(bucketIndex + 1).join('/');
      
      // Delete from S3
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });
      
      await this.s3Client.send(command);
    } catch (error) {
      console.error('S3 delete error:', error);
      throw new Error(`Failed to delete photo from S3: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if S3 is configured and available
   * @returns boolean
   */
  isConfigured(): boolean {
    return !!(
      process.env.S3_ACCESS_KEY &&
      process.env.S3_SECRET_KEY &&
      this.bucketName
    );
  }
}

// Export a singleton instance
export const s3Service = new S3Service();
