/* eslint-disable no-console */
import { PrismaClient } from '@prisma/client';
import nodemailer from 'nodemailer';

import { EmailOptions, IEmailService } from '../interfaces/IEmailService';
import { Language, EmailFormat } from '../types/models';
import {
  generateWelcomeEmail,
  generateVerificationOTPEmail,
  generatePasswordResetEmail,
  generateShipmentCreatedEmail,
  generatePasswordChangedEmail,
  generateShipmentStatusUpdateEmail,
  EmailTemplateOptions
} from '../utils/bilingualEmailTemplates';
import {
  adminOTPEmailTemplate,
  adminPasswordChangedEmailTemplate,
  adminPasswordResetEmailTemplate,
  adminWelcomeEmailTemplate,
  availableShipmentCOTemplate,
  newShipmentNotificationAOTemplate,
  passwordChangedEmailTemplate,
  passwordResetEmailTemplate,
  shipmentCreatedEmailTemplate,
  shipmentExpiredEmailTemplate,
  shipmentStatusUpdateEmailTemplate,
  verificationEmailTemplate,
  verificationOTPEmailTemplate,
  welcomeEmailTemplate
} from '../utils/emailTemplates';
import {
  generateModernShipmentUpdateEmail,
  generateModernShipmentCreatedEmail
} from '../utils/modernEmailTemplates';


export class EmailService implements IEmailService {
  private transporter!: nodemailer.Transporter | any; // Using definite assignment assertion
  private isTransporterReady: boolean = false;
  private transporterReadyPromise: Promise<void>;
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    // Initialize the transporter ready promise
    this.transporterReadyPromise = this.initializeTransporter();
  }

  private async initializeTransporter(): Promise<void> {
    try {
      // Use Ethereal (fake SMTP service) only if explicitly requested
      if (process.env.USE_ETHEREAL_EMAIL === 'true') {
        await this.createTestTransporter();
      } else {
        // For both development and production, use the configured SMTP server
        console.log('Initializing real SMTP email transporter...');
        this.transporter = nodemailer.createTransport({
          host: process.env.EMAIL_HOST || 'smtp.gmail.com',
          port: parseInt(process.env.EMAIL_PORT || '587'),
          secure: false, // true for 465, false for other ports
          auth: {
            user: process.env.EMAIL_USERNAME || '<EMAIL>',
            pass: process.env.EMAIL_PASSWORD || 'wljv dcss mcei hslq',
          },
        });
        console.log('Real SMTP email transporter initialized successfully');
      }

      this.isTransporterReady = true;
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
      this.createFallbackTransporter();
    }
  }

  private async createTestTransporter() {
    try {
      // Create a test account using Ethereal
      console.log('Creating Ethereal test account...');
      const testAccount = await nodemailer.createTestAccount();
      console.log('Created Ethereal test account:', testAccount.user);

      // Create a transporter using the test account
      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
        debug: true, // Enable debug output
      });

      console.log('Ethereal email transporter created successfully');
    } catch (error) {
      console.error('Failed to create Ethereal test account:', error);
      // Fallback to a dummy transporter
      this.createFallbackTransporter();
    }
  }

  private createFallbackTransporter() {
    console.log('Creating fallback email transporter (logs only)');
    this.transporter = {
      sendMail: (mailOptions: any) => {
        console.log('Email would have been sent:');
        console.log('To:', mailOptions.to);
        console.log('Subject:', mailOptions.subject);
        console.log('Content:', mailOptions.text || mailOptions.html);
        return Promise.resolve({ messageId: 'dummy-message-id' });
      }
    } as any;
    this.isTransporterReady = true;
  }

  /**
   * Get user email preferences
   */
  private async getUserEmailPreferences(email: string): Promise<EmailTemplateOptions> {
    try {
      // Find user by email
      const user = await this.prisma.user.findUnique({
        where: { email },
        include: {
          notificationPreferences: true
        }
      });

      if (user?.notificationPreferences) {
        return {
          preferredLanguage: user.notificationPreferences.preferred_language as Language,
          emailFormat: user.notificationPreferences.email_format as EmailFormat
        };
      }

      // Default preferences if user not found or no preferences set
      return {
        preferredLanguage: Language.ENGLISH,
        emailFormat: EmailFormat.BILINGUAL
      };
    } catch (error) {
      console.error('Error getting user email preferences:', error);
      // Return default preferences on error
      return {
        preferredLanguage: Language.ENGLISH,
        emailFormat: EmailFormat.BILINGUAL
      };
    }
  }

  /**
   * Send an email
   */
  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      // Wait for the transporter to be ready before sending
      if (!this.isTransporterReady) {
        console.log('Waiting for email transporter to be ready...');
        await this.transporterReadyPromise;
      }

      console.log(`Attempting to send email to: ${options.to}, subject: ${options.subject}`);

      const mailOptions = {
        from: `"Shipment Relay Platform" <${
          process.env.EMAIL_USERNAME || '<EMAIL>'
        }>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      console.log('Mail options:', JSON.stringify({
        to: mailOptions.to,
        subject: mailOptions.subject,
        from: mailOptions.from
      }));

      if (!this.transporter) {
        console.error('Transporter is not initialized!');
        throw new Error('Email transporter is not initialized');
      }

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', info.messageId);

      // If using Ethereal, show the preview URL
      if (process.env.USE_ETHEREAL_EMAIL === 'true' && nodemailer.getTestMessageUrl) {
        const previewUrl = nodemailer.getTestMessageUrl(info);
        if (previewUrl) {
          console.log('Preview URL:', previewUrl);
          console.log('⚠️ NOTE: This is a test email using Ethereal. Use the preview URL to view it.');
        }
      } else {
        console.log('Email sent using real SMTP server');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      // Don't throw an error, just log it - this prevents the registration process from failing
      // if email sending fails
      console.error('Email sending failed but continuing with registration process');
    }
  }

  /**
   * Send a welcome email to a new user
   */
  async sendWelcomeEmail(email: string, name: string, userType: string): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateWelcomeEmail(name, userType, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending welcome email:', error);
      // Fallback to English template
      const subject = 'Welcome to Shipment Relay Platform';
      const html = welcomeEmailTemplate(name, userType);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a verification email with OTP
   */
  async sendVerificationOTP(
    email: string,
    name: string,
    otp: string,
  ): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateVerificationOTPEmail(name, otp, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending verification OTP email:', error);
      // Fallback to English template
      const subject = 'Verify Your Email - Shipment Relay Platform';
      const html = verificationOTPEmailTemplate(name, otp);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a verification email (Legacy method)
   */
  async sendVerificationEmail(
    email: string,
    name: string,
    verificationLink: string,
  ): Promise<void> {
    const subject = 'Verify Your Email - Shipment Relay Platform';
    const html = verificationEmailTemplate(name, verificationLink);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send a password reset email
   */
  async sendPasswordResetEmail(email: string, name: string, resetLink: string): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generatePasswordResetEmail(name, resetLink, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending password reset email:', error);
      // Fallback to English template
      const subject = 'Password Reset - Shipment Relay Platform';
      const html = passwordResetEmailTemplate(name, resetLink);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a password changed confirmation email
   */
  async sendPasswordChangedEmail(email: string, name: string): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generatePasswordChangedEmail(name, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending password changed email:', error);
      // Fallback to English template
      const subject = 'Password Changed - Shipment Relay Platform';
      const html = passwordChangedEmailTemplate(name);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a shipment created notification
   */
  async sendShipmentCreatedEmail(
    email: string,
    name: string,
    shipmentId: string,
    pickupCode: string,
  ): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateShipmentCreatedEmail(name, shipmentId, pickupCode, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending shipment created email:', error);
      // Fallback to English template
      const subject = 'Shipment Created - Shipment Relay Platform';
      const html = shipmentCreatedEmailTemplate(name, shipmentId, pickupCode);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a shipment status update notification
   */
  async sendShipmentStatusUpdateEmail(
    email: string,
    name: string,
    shipmentId: string,
    status: string,
    details?: string,
  ): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateShipmentStatusUpdateEmail(name, shipmentId, status, details, preferences);

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending shipment status update email:', error);
      // Fallback to English template
      const subject = `Shipment Update: ${status} - Shipment Relay Platform`;
      const html = shipmentStatusUpdateEmailTemplate(name, shipmentId, status, details);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send a notification to Access Operator about new shipment
   */
  async sendNewShipmentNotificationToAO(
    email: string,
    businessName: string,
    shipmentId: string,
  ): Promise<void> {
    const subject = 'New Shipment Alert - Shipment Relay Platform';
    const html = newShipmentNotificationAOTemplate(businessName, shipmentId);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send a notification to Car Operator about available shipment
   */
  async sendAvailableShipmentToCO(
    email: string,
    name: string,
    shipmentId: string,
    originLocation: string,
    destLocation: string,
  ): Promise<void> {
    const subject = 'Available Shipment - Shipment Relay Platform';
    const html = availableShipmentCOTemplate(name, shipmentId, originLocation, destLocation);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send a welcome email to a new admin
   */
  async sendAdminWelcomeEmail(email: string, name: string, role: string): Promise<void> {
    try {
      console.log(`Preparing admin welcome email for: ${email}, name: ${name}, role: ${role}`);
      const subject = 'Welcome to Shipment Relay Platform Admin Portal';
      const html = adminWelcomeEmailTemplate(name, role);

      console.log('Admin welcome email template generated, sending email...');
      await this.sendEmail({
        to: email,
        subject,
        html,
      });
      console.log('Admin welcome email sending process completed');
    } catch (error) {
      console.error('Error in sendAdminWelcomeEmail:', error);
      // Don't throw the error to prevent registration failure
      console.error('Admin welcome email failed but continuing with registration process');
    }
  }

  /**
   * Send an admin password reset email
   */
  async sendAdminPasswordResetEmail(email: string, name: string, resetLink: string): Promise<void> {
    const subject = 'Admin Password Reset - Shipment Relay Platform';
    const html = adminPasswordResetEmailTemplate(name, resetLink);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send an admin password changed confirmation email
   */
  async sendAdminPasswordChangedEmail(email: string, name: string): Promise<void> {
    const subject = 'Admin Password Changed - Shipment Relay Platform';
    const html = adminPasswordChangedEmailTemplate(name);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send an OTP verification email to admin
   */
  async sendAdminOTPEmail(email: string, name: string, otp: string): Promise<void> {
    try {
      console.log(`Preparing admin OTP email for: ${email}, name: ${name}, otp: ${otp}`);
      const subject = 'Admin Account Verification - Shipment Relay Platform';
      const html = adminOTPEmailTemplate(name, otp);

      console.log('Admin OTP email template generated, sending email...');
      await this.sendEmail({
        to: email,
        subject,
        html,
      });
      console.log('Admin OTP email sending process completed');
    } catch (error) {
      console.error('Error in sendAdminOTPEmail:', error);
      // Don't throw the error to prevent registration failure
      console.error('Admin OTP email failed but continuing with process');
    }
  }

  /**
   * Send a shipment expired notification email
   */
  async sendShipmentExpiredEmail(
    email: string,
    name: string,
    shipmentId: string,
  ): Promise<void> {
    const subject = 'Shipment Automatically Cancelled - Shipment Relay Platform';
    const html = shipmentExpiredEmailTemplate(name, shipmentId);

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send shipment status change email with enhanced templates
   */
  async sendShipmentStatusEmail(
    to: string,
    recipientName: string,
    shipmentId: string,
    status: string,
    trackingCode: string,
    additionalInfo?: any
  ): Promise<void> {
    const { subject, text, html } = this.getStatusEmailContent(
      recipientName,
      shipmentId,
      status,
      trackingCode,
      additionalInfo
    );

    await this.sendEmail({ to, subject, text, html });
  }

  /**
   * Get email content based on shipment status
   */
  private getStatusEmailContent(
    recipientName: string,
    shipmentId: string,
    status: string,
    trackingCode: string,
    additionalInfo?: any
  ): { subject: string; text: string; html: string } {

    switch (status) {
      case 'PENDING':
        return {
          subject: 'Shipment Created - 24 Hour Timer Started',
          text: `Dear ${recipientName},\n\nYour shipment has been created successfully!\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\n\nIMPORTANT: You have 24 hours to drop off your package at the origin access point. After this time, your shipment will be automatically cancelled.\n\nPickup Code: ${additionalInfo?.pickupCode}\nOrigin Access Point: ${additionalInfo?.originAO}\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">📦 Shipment Created Successfully!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Your shipment has been created and is ready for drop-off.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #1f2937;">Shipment Details</h3>
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Pickup Code:</strong> ${additionalInfo?.pickupCode}</p>
                <p><strong>Origin Access Point:</strong> ${additionalInfo?.originAO}</p>
              </div>

              <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #92400e;">⏰ Important: 24-Hour Timer Active</h4>
                <p style="margin-bottom: 0;">You have <strong>24 hours</strong> to drop off your package at the origin access point. After this time, your shipment will be automatically cancelled.</p>
              </div>

              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'AWAITING_PICKUP':
        return {
          subject: 'Package Dropped Off - Timer Stopped',
          text: `Dear ${recipientName},\n\nGreat news! Your package has been successfully dropped off at the origin access point.\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\n\nThe 24-hour timer has been stopped. Your package is now waiting for a car operator to pick it up.\n\nWe'll notify you when a car operator picks up your package.\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #059669;">✅ Package Dropped Off Successfully!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Great news! Your package has been successfully dropped off at the origin access point.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Status:</strong> Awaiting Car Operator Pickup</p>
              </div>

              <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #065f46;">⏰ Timer Stopped</h4>
                <p style="margin-bottom: 0;">The 24-hour timer has been stopped. Your package is now safely waiting for a car operator to pick it up.</p>
              </div>

              <p>We'll notify you when a car operator picks up your package.</p>
              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'PICKED_UP_BY_CO':
        return {
          subject: 'Package Picked Up by Car Operator',
          text: `Dear ${recipientName},\n\nYour package has been picked up by a car operator and is now on its way to the destination.\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nCar Operator: ${additionalInfo?.carOperatorName}\n\nWe'll notify you when your package arrives at the destination access point.\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">🚗 Package Picked Up!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Your package has been picked up by a car operator and is now on its way to the destination.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Car Operator:</strong> ${additionalInfo?.carOperatorName}</p>
                <p><strong>Status:</strong> In Transit</p>
              </div>

              <p>We'll notify you when your package arrives at the destination access point.</p>
              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'IN_TRANSIT':
        return {
          subject: 'Package In Transit',
          text: `Dear ${recipientName},\n\nYour package is currently in transit to the destination.\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nCar Operator: ${additionalInfo?.carOperatorName}\n\nEstimated arrival: ${additionalInfo?.estimatedArrival || 'Soon'}\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">🚛 Package In Transit</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Your package is currently on its way to the destination access point.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Car Operator:</strong> ${additionalInfo?.carOperatorName}</p>
                <p><strong>Status:</strong> In Transit</p>
                <p><strong>Estimated Arrival:</strong> ${additionalInfo?.estimatedArrival || 'Soon'}</p>
              </div>

              <p>We'll notify you as soon as your package arrives at the destination.</p>
              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'ARRIVED_AT_DESTINATION':
        return {
          subject: 'Package Arrived at Destination',
          text: `Dear ${recipientName},\n\nYour package has arrived at the destination access point and is ready for pickup!\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nDestination: ${additionalInfo?.destinationAO}\n\nYou can now visit the destination access point to collect your package.\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #059669;">🎯 Package Arrived at Destination!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Great news! Your package has arrived at the destination access point and is ready for pickup.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Destination:</strong> ${additionalInfo?.destinationAO}</p>
                <p><strong>Status:</strong> Ready for Pickup</p>
              </div>

              <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #065f46;">📍 Ready for Collection</h4>
                <p style="margin-bottom: 0;">You can now visit the destination access point to collect your package.</p>
              </div>

              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'READY_FOR_DELIVERY':
        return {
          subject: 'Package Ready for Pickup',
          text: `Dear ${recipientName},\n\nYour package is ready for pickup at the destination access point!\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nDestination: ${additionalInfo?.destinationAO}\nAddress: ${additionalInfo?.destinationAddress}\n\nPlease visit the access point to collect your package.\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #059669;">📦 Ready for Pickup!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Your package is ready for pickup at the destination access point!</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Destination:</strong> ${additionalInfo?.destinationAO}</p>
                <p><strong>Address:</strong> ${additionalInfo?.destinationAddress}</p>
                <p><strong>Status:</strong> Ready for Pickup</p>
              </div>

              <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #065f46;">🎯 Action Required</h4>
                <p style="margin-bottom: 0;">Please visit the destination access point to collect your package.</p>
              </div>

              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      case 'DELIVERED':
        return {
          subject: 'Package Delivered Successfully!',
          text: `Dear ${recipientName},\n\nCongratulations! Your package has been delivered successfully.\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nDelivered at: ${additionalInfo?.deliveredAt}\n\nThank you for using Naqalat Platform!\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #059669;">🎉 Package Delivered Successfully!</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Congratulations! Your package has been delivered successfully.</p>

              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>Delivered at:</strong> ${additionalInfo?.deliveredAt}</p>
                <p><strong>Status:</strong> ✅ Completed</p>
              </div>

              <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #065f46;">🎯 Mission Accomplished!</h4>
                <p style="margin-bottom: 0;">Your shipment journey is complete. Thank you for choosing Naqalat Platform!</p>
              </div>

              <p>We hope you had a great experience. Feel free to create another shipment anytime!</p>
              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };

      default:
        return {
          subject: 'Shipment Status Update',
          text: `Dear ${recipientName},\n\nYour shipment status has been updated.\n\nShipment ID: ${shipmentId}\nTracking Code: ${trackingCode}\nNew Status: ${status}\n\nBest regards,\nNaqalat Platform`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>Shipment Status Update</h2>
              <p>Dear <strong>${recipientName}</strong>,</p>
              <p>Your shipment status has been updated.</p>
              <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Shipment ID:</strong> ${shipmentId}</p>
                <p><strong>Tracking Code:</strong> ${trackingCode}</p>
                <p><strong>New Status:</strong> ${status}</p>
              </div>
              <p>Best regards,<br><strong>Naqalat Platform</strong></p>
            </div>
          `
        };
    }
  }

  /**
   * Send modern shipment created notification
   */
  async sendModernShipmentCreatedEmail(
    email: string,
    name: string,
    shipmentId: string,
    pickupCode: string,
    trackingCode: string,
  ): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateModernShipmentCreatedEmail(
        name,
        shipmentId,
        pickupCode,
        trackingCode,
        preferences
      );

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending modern shipment created email:', error);
      // Fallback to regular template
      const subject = 'Shipment Created - Shipment Relay Platform';
      const html = shipmentCreatedEmailTemplate(name, shipmentId, pickupCode);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }

  /**
   * Send modern shipment status update notification
   */
  async sendModernShipmentStatusUpdateEmail(
    email: string,
    name: string,
    shipmentId: string,
    status: string,
    trackingCode: string,
    details?: string,
  ): Promise<void> {
    try {
      const preferences = await this.getUserEmailPreferences(email);
      const emailTemplate = generateModernShipmentUpdateEmail(
        name,
        shipmentId,
        status,
        trackingCode,
        preferences,
        details
      );

      await this.sendEmail({
        to: email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      });
    } catch (error) {
      console.error('Error sending modern shipment status update email:', error);
      // Fallback to regular template
      const subject = `Shipment Update: ${status} - Shipment Relay Platform`;
      const html = shipmentStatusUpdateEmailTemplate(name, shipmentId, status, details);

      await this.sendEmail({
        to: email,
        subject,
        html,
      });
    }
  }
}
