import { AdminRole, UserStatus } from '@prisma/client';
import { z } from 'zod';

// Common schemas
export const UUIDSchema = z.string().uuid();
export const EmailSchema = z.string().email();
export const PasswordSchema = z.string().min(8).max(100);
export const PhoneSchema = z.string().min(10).max(15);
export const TimestampSchema = z.date();

// Admin schemas
export const AdminRoleSchema = z.nativeEnum(AdminRole);
export const AdminStatusSchema = z.nativeEnum(UserStatus);

export const AdminSchema = z.object({
  id: UUIDSchema,
  name: z.string().min(2).max(100),
  email: EmailSchema,
  password_hash: z.string(),
  role: AdminRoleSchema,
  status: AdminStatusSchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

export const AdminCreateSchema = z.object({
  name: z.string().min(2).max(100),
  email: EmailSchema,
  password: PasswordSchema,
  role: AdminRoleSchema.optional(),
}).strict(); // Strict mode rejects any extra fields

export const AdminLoginSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
}).strict();

export const AdminUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  role: AdminRoleSchema.optional(),
}).strict();

export const AdminChangePasswordSchema = z.object({
  currentPassword: PasswordSchema,
  newPassword: PasswordSchema,
}).strict();

export const AdminForgotPasswordSchema = z.object({
  email: EmailSchema,
}).strict();

export const AdminResetPasswordSchema = z.object({
  email: EmailSchema,
  token: z.string(),
  password: PasswordSchema,
}).strict();

export const AdminChangeStatusSchema = z.object({
  id: UUIDSchema,
  status: AdminStatusSchema,
}).strict();

export const AdminVerifyOTPSchema = z.object({
  email: EmailSchema,
  otp: z.string().length(6),
}).strict();

export const UpdateUserStatusSchema = z.object({
  userId: z.string().uuid(),
  status: z.nativeEnum(UserStatus),
});
