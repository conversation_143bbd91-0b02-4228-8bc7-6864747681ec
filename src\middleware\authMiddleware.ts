import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import { AppError } from '../utils/errors';

/**
 * Authentication middleware to verify JWT token
 * This middleware checks if a valid JWT token is provided in the Authorization header
 * and adds the decoded user information to the request object
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw AppError.unauthorized('No token provided');
    }

    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // Add user data to request
    req.user = decoded as jwt.JwtPayload;

    // Proceed to next middleware
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      AppError.handleError(AppError.unauthorized('Invalid token'), req, res);
    } else {
      AppError.handleError(error, req, res);
    }
  }
};
