import { PrismaClient } from '@prisma/client';

import { IAuditLogService } from '../interfaces/services';
import { AuditLog } from '../types/models';

export class AuditLogService implements IAuditLogService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createAuditLog(
    userId: string | undefined,
    action: string,
    shipmentId?: string,
    details: Record<string, any> = {},
    adminId?: string,
  ): Promise<AuditLog> {
    // Determine if this is a user or admin action
    const data: any = {
      action,
      details,
    };

    // Add user_id if provided
    if (userId) {
      data.user_id = userId;
    }

    // Add admin_id if provided
    if (adminId) {
      data.admin_id = adminId;
    }

    // Add shipment_id if provided
    if (shipmentId) {
      data.shipment_id = shipmentId;
    }

    const auditLog = await this.prisma.auditLog.create({
      data,
    });

    return auditLog as AuditLog;
  }

  async getAuditLogsByShipmentId(shipmentId: string): Promise<AuditLog[]> {
    const auditLogs = await this.prisma.auditLog.findMany({
      where: { shipment_id: shipmentId },
      orderBy: { created_at: 'desc' },
    });

    return auditLogs as AuditLog[];
  }

  async getAuditLogsByUserId(userId: string): Promise<AuditLog[]> {
    const auditLogs = await this.prisma.auditLog.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' },
    });

    return auditLogs as AuditLog[];
  }
}
