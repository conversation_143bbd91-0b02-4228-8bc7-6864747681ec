import { z } from 'zod';

import { UserType, UserStatus, ShipmentStatus, QRLabelStatus } from '../types/models';

// Common schemas
export const UUIDSchema = z.string().uuid();
export const EmailSchema = z.string().email();
export const PasswordSchema = z.string().min(8).max(100);
export const PhoneSchema = z.string().min(8).max(15);
export const TimestampSchema = z.date();
export const GeoPointSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
});

// User schemas
export const UserTypeSchema = z.nativeEnum(UserType);
export const UserStatusSchema = z.nativeEnum(UserStatus);

export const UserSchema = z.object({
  id: UUIDSchema,
  name: z.string().min(2).max(100),
  email: EmailSchema,
  password_hash: z.string(),
  phone: PhoneSchema,
  user_type: UserTypeSchema,
  status: UserStatusSchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

export const UserCreateSchema = z.object({
  name: z.string().min(2).max(100),
  email: EmailSchema,
  password: PasswordSchema,
  phone: PhoneSchema,
  user_type: UserTypeSchema,
});

// Simplified registration schemas - only basic information required
// Users can complete their profiles after registration

// Base registration schema for all user types - only essential fields
export const BaseRegisterSchema = z.object({
  name: z.string().min(2).max(100),
  email: EmailSchema,
  password: PasswordSchema,
  phone: PhoneSchema,
  user_type: z.nativeEnum(UserType),
}).strict(); // Strict mode rejects any extra fields

// Individual schemas for backward compatibility (all now use base fields only)
export const CustomerRegisterSchema = BaseRegisterSchema.extend({
  user_type: z.literal(UserType.CUSTOMER),
}).strict();

export const AccessOperatorRegisterSchema = BaseRegisterSchema.extend({
  user_type: z.literal(UserType.ACCESS_OPERATOR),
}).strict();

export const CarOperatorRegisterSchema = BaseRegisterSchema.extend({
  user_type: z.literal(UserType.CAR_OPERATOR),
}).strict();

// Extended registration schema that selects the appropriate schema based on user_type
// This is used for backward compatibility and for the initial user_type determination
export const ExtendedRegisterSchema = z.discriminatedUnion("user_type", [
  CustomerRegisterSchema,
  AccessOperatorRegisterSchema,
  CarOperatorRegisterSchema,
]);

export const UserLoginSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
}).strict();

// OTP Verification schemas
export const SendOTPSchema = z.object({
  email: EmailSchema,
}).strict();

export const VerifyOTPSchema = z.object({
  otp: z.string().length(6, 'OTP must be exactly 6 digits').regex(/^\d{6}$/,'OTP must contain only digits'),
}).strict();

export const ResendOTPSchema = z.object({
  email: EmailSchema,
}).strict();

// Combined update schema that accepts all possible fields
// The controller will filter out fields based on user type
export const UserUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  phone: PhoneSchema.optional(),
  // Access Operator fields
  business_name: z.string().min(2).max(100).optional(),
  address: z.string().min(5).max(200).optional(),
  geo_latitude: z.number().min(-90).max(90).nullable().optional(),
  geo_longitude: z.number().min(-180).max(180).nullable().optional(),
  // Car Operator fields
  license_number: z.string().min(5).max(20).optional(),
  vehicle_info: z.string().min(5).max(200).optional(),
  pickup_access_point_id: z.string().transform((val) => val === '' ? null : val).pipe(UUIDSchema.nullable()).optional(),
  dropoff_access_point_id: z.string().transform((val) => val === '' ? null : val).pipe(UUIDSchema.nullable()).optional(),
});

// Access Operator schemas
export const AccessOperatorCreateSchema = z.object({
  user: UserCreateSchema,
  business_name: z.string().min(2).max(100),
  address: z.string().min(5).max(200),
  geo_location: GeoPointSchema,
});

export const AccessOperatorUpdateSchema = z.object({
  business_name: z.string().min(2).max(100).optional(),
  address: z.string().min(5).max(200).optional(),
  geo_location: GeoPointSchema.optional(),
});

// Access Point Update Schema - includes both user and access operator fields
export const AccessPointUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  phone: PhoneSchema.optional(),
  business_name: z.string().min(2).max(100).optional(),
  address: z.string().min(5).max(200).optional(),
  geo_latitude: z.number().min(-90).max(90).optional(),
  geo_longitude: z.number().min(-180).max(180).optional(),
}).strict();

// Car Operator schemas
export const CarOperatorCreateSchema = z.object({
  user: UserCreateSchema,
  license_number: z.string().min(5).max(20),
  vehicle_info: z.string().min(5).max(200),
});

export const CarOperatorUpdateSchema = z.object({
  license_number: z.string().min(5).max(20).optional(),
  vehicle_info: z.string().min(5).max(200).optional(),
});

// Shipment schemas
export const ShipmentStatusSchema = z.nativeEnum(ShipmentStatus);

export const ShipmentCreateSchema = z.object({
  origin_ao_id: UUIDSchema,
  dest_ao_id: UUIDSchema,
  weight: z.number().positive(),
  size: z.string().min(1).max(50),
  description: z.string().min(5).max(500),
  receiver_name: z.string().min(1).max(100),
  receiver_phone: z.string().min(10).max(20),
});

export const ShipmentUpdateSchema = z.object({
  weight: z.number().positive().optional(),
  size: z.string().min(1).max(50).optional(),
  description: z.string().min(5).max(500).optional(),
});

export const ShipmentQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  pageSize: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(), // backward compatibility
  search: z.string().optional(),
  status: z.string().optional(),
  sort: z.string().optional(),
  sortBy: z.string().optional(), // Support for sortBy parameter
  sortOrder: z.enum(['asc', 'desc']).optional(), // Support for sortOrder parameter
  originAoId: UUIDSchema.optional(),
  destAoId: UUIDSchema.optional(),
  filter: z.string().optional(), // Support for complex filter objects as JSON string
  skip: z.string().regex(/^-?\d+$/).transform(Number).optional(), // Support for skip parameter (allow negative for graceful handling)
  take: z.string().regex(/^\d+$/).transform(Number).optional(), // Support for take parameter
});

// QR Label schemas
export const QRLabelStatusSchema = z.nativeEnum(QRLabelStatus);

export const QRLabelGenerateSchema = z.object({
  count: z.number().int().min(1).max(100),
});

export const QRLabelAssignSchema = z.object({
  qr_label_id: UUIDSchema,
  shipment_id: UUIDSchema,
});

// Base64 image validation schema
const Base64ImageSchema = z.string()
  .min(1, 'Photo is required')
  .refine(
    (value) => {
      // Check if it's a valid base64 image
      const base64Regex = /^data:image\/(jpeg|jpg|png|gif);base64,/;
      return base64Regex.test(value);
    },
    {
      message: 'Invalid image format. Must be a base64 encoded JPEG, PNG, or GIF image',
    }
  )
  .refine(
    (value) => {
      // Check file size (approximate, base64 is ~33% larger than binary)
      const base64Data = value.replace(/^data:image\/[a-z]+;base64,/, '');
      const sizeInBytes = (base64Data.length * 3) / 4;
      const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
      return sizeInBytes <= maxSizeInBytes;
    },
    {
      message: 'Image size must be less than 5MB',
    }
  );

// Photo URL validation schema (accepts both relative paths and full URLs)
const PhotoUrlSchema = z.string().min(1, 'Photo URL is required').refine(
  (url) => url.startsWith('/uploads/') || url.startsWith('https://'),
  'Photo URL must be a valid upload path or HTTPS URL'
);

// QR Assignment Request Schema (Updated to use photo URL)
export const QRAssignmentRequestSchema = z.object({
  shipmentId: UUIDSchema,
  shipmentQRValue: z.string().min(1, 'Shipment QR value is required'),
  qrLabelId: UUIDSchema,
  notes: z.string().max(500).optional(),
  geoLatitude: z.number().min(-90).max(90).optional(),
  geoLongitude: z.number().min(-180).max(180).optional(),
  photoUrl: PhotoUrlSchema,
});

// Removed: HandoverQRRequestSchema - no longer needed with simplified workflow

// QR Validation Request Schema
export const QRValidationRequestSchema = z.object({
  qrValue: z.string().min(1, 'QR value is required'),
  shipmentId: UUIDSchema,
});

// QR Scan Request Schema (for AO QR scanning)
export const QRScanRequestSchema = z.object({
  qrValue: z.string().min(1, 'QR value is required'),
});

// Photo Upload Request Schema
export const PhotoUploadRequestSchema = z.object({
  photoBase64: Base64ImageSchema,
  folder: z.string().optional().default('qr-assignment'),
});

// Shipment Scan Request Schema - requires photo URL (uploaded separately)
// Geo coordinates are auto-extracted based on action and user location
export const ShipmentScanRequestSchema = z.object({
  qr_value: z.string().min(1, 'QR value is required'),
  photo_url: PhotoUrlSchema,
  action: z.enum(['DROPOFF', 'PICKUP', 'ARRIVAL'], {
    errorMap: () => ({ message: 'Action must be DROPOFF, PICKUP, or ARRIVAL' })
  }),
  notes: z.string().max(500).optional(),
  // Required shipment_id - frontend always sends this for auto-assigning unused QR codes
  shipment_id: UUIDSchema,
  // geo_latitude and geo_longitude removed - will be auto-extracted based on action:
  // DROPOFF: from origin AO, PICKUP: from origin AO, ARRIVAL: from destination AO
});

// Shipment Delivery Request Schema - requires photo URL (uploaded separately)
// Geo coordinates are auto-extracted from destination AO
export const ShipmentDeliveryRequestSchema = z.object({
  shipment_qr: z.string().min(1, 'Shipment QR is required'),
  pickup_qr: z.string().min(1, 'Pickup QR is required'),
  photo_url: PhotoUrlSchema,
  notes: z.string().max(500).optional(),
  receiver_phone: z.string().min(10).max(20).optional(),
  // geo_latitude and geo_longitude removed - will be auto-extracted from destination AO
});

export const PickupSchema = z.object({
  shipment_id: UUIDSchema,
  qr_label_id: UUIDSchema,
  route_point_id: UUIDSchema,
  from_user_id: UUIDSchema,
  photo_url: PhotoUrlSchema,
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  notes: z.string().max(500).optional(),
});

export const ArrivalSchema = z.object({
  shipment_id: UUIDSchema,
  qr_label_id: UUIDSchema,
  route_point_id: UUIDSchema,
  to_user_id: UUIDSchema,
  photo_url: PhotoUrlSchema,
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  notes: z.string().max(500).optional(),
});

export const DeliverySchema = z.object({
  shipment_id: UUIDSchema,
  qr_label_id: UUIDSchema,
  route_point_id: UUIDSchema,
  to_user_id: UUIDSchema,
  pickup_code: z.string().min(6).max(6),
  photo_url: PhotoUrlSchema,
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  notes: z.string().max(500).optional(),
});

// Notification schemas
export const NotificationCreateSchema = z.object({
  user_id: UUIDSchema,
  shipment_id: UUIDSchema,
  type: z.string().min(1).max(50),
  message: z.string().min(1).max(500),
});

// Dashboard schemas
export const DashboardResponseSchema = z.object({
  user_info: z.object({
    id: UUIDSchema,
    name: z.string(),
    email: z.string().email(),
    user_type: UserTypeSchema,
    status: UserStatusSchema,
  }),
  dashboard_data: z.record(z.any()), // Key-value pairs for dashboard data
});

// Rating schemas
export const RatingCreateSchema = z.object({
  shipment_id: UUIDSchema,
  to_user_id: UUIDSchema,
  rating: z.number().int().min(1).max(5),
  comment: z.string().min(1).max(500),
});
