import { Request, Response } from 'express';

import { s3Service } from '../services/S3Service';

export class PhotoUploadController {
  /**
   * Upload photo for QR assignment
   * POST /uploads/photo
   */
  static async uploadPhoto(req: Request, res: Response): Promise<void> {
    try {
      console.info('=== PHOTO UPLOAD REQUEST START ===');
      console.info('Request headers:', req.headers);
      console.info('Request body keys:', Object.keys(req.body));
      console.info('Request body size:', JSON.stringify(req.body).length);

      const userId = req.user?.id;
      const userType = req.user?.user_type;

      console.info('User info:', { userId, userType });

      // Verify user is authenticated and is an access operator
      if (!userId) {
        console.error('ERROR: No userId found - unauthorized');
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
          error: {
            type: 'UNAUTHORIZED',
            details: 'Authentication required',
          },
        });
        return;
      }

      // Allow all authenticated users to upload photos

      // Simple validation for photo_base64 (accept both camelCase and snake_case)
      const { photo_base64, photoBase64 } = req.body;
      const photoData = photo_base64 || photoBase64;

      if (!photoData) {
        console.error('ERROR: Missing photo_base64 or photoBase64 field');
        res.status(400).json({
          success: false,
          message: 'Missing photo data field',
          error: {
            type: 'VALIDATION_ERROR',
            details: 'photo_base64 or photoBase64 is required',
          },
        });
        return;
      }

      console.info('Photo upload request:', {
        userId,
        userType,
        photoSize: photoData.length
      });

      // Validate base64 image format
      const isValidImage = PhotoUploadController.validateBase64Image(photoData);

      if (!isValidImage) {
        console.error('Invalid image format detected');
        res.status(422).json({
          success: false,
          message: 'Invalid image format',
          error: {
            type: 'VALIDATION_ERROR',
            details: 'Must be a base64 encoded JPEG, PNG, or GIF image',
          },
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        console.error('S3 is not properly configured');
        res.status(500).json({
          success: false,
          message: 'File upload service not available',
          error: {
            type: 'CONFIGURATION_ERROR',
            details: 'S3 service is not properly configured',
          },
        });
        return;
      }

      // Process and store photo using S3
      const photoUrl = await s3Service.uploadPhotoFromBase64(
        photoData,
        'general' // Default folder
      );

      console.info('Photo uploaded successfully:', {
        userId,
        photoUrl
      });

      res.status(201).json({
        success: true,
        message: 'Photo uploaded successfully',
        data: {
          photo_url: photoUrl
        },
      });
    } catch (error) {
      console.error('=== PHOTO UPLOAD ERROR ===');
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        type: typeof error,
        error
      });

      if (error instanceof Error) {
        // Handle photo processing errors
        if (error.message.includes('Invalid image format') || error.message.includes('Image size')) {
          console.error('Photo validation error:', error.message);
          res.status(422).json({
            success: false,
            message: 'Photo processing error',
            error: {
              type: 'FILE_UPLOAD_ERROR',
              details: error.message,
            },
          });
          return;
        }
      }

      console.error('Unhandled photo upload error');
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: {
          type: 'INTERNAL_ERROR',
          details: 'Failed to upload photo',
        },
      });
    }
  }

  /**
   * Upload photo using multipart form data
   * POST /uploads/photo/multipart
   */
  static async uploadPhotoMultipart(req: Request, res: Response): Promise<void> {
    try {
      console.info('=== MULTIPART PHOTO UPLOAD REQUEST START ===');
      console.info('Request headers:', req.headers);
      console.info('Request file:', req.file ? {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      } : 'No file');

      const userId = req.user?.id;
      const userType = req.user?.user_type;

      console.info('User info:', { userId, userType });

      // Verify user is authenticated
      if (!userId) {
        console.error('ERROR: No userId found - unauthorized');
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
          error: {
            type: 'UNAUTHORIZED',
            details: 'Authentication required',
          },
        });
        return;
      }

      // Check if file was uploaded
      if (!req.file) {
        console.error('ERROR: No file uploaded');
        res.status(400).json({
          success: false,
          message: 'No file uploaded',
          error: {
            type: 'VALIDATION_ERROR',
            details: 'A photo file is required',
          },
        });
        return;
      }

      // Check if S3 upload was successful (should be set by uploadToS3 middleware)
      const photoUrl = req.body.s3_photo_url;

      if (!photoUrl) {
        console.error('ERROR: S3 upload failed or not configured');
        res.status(500).json({
          success: false,
          message: 'File upload failed',
          error: {
            type: 'UPLOAD_ERROR',
            details: 'Failed to upload file to storage service',
          },
        });
        return;
      }

      console.info('Multipart photo uploaded successfully:', {
        userId,
        photoUrl,
        originalName: req.file.originalname,
        size: req.file.size
      });

      res.status(201).json({
        success: true,
        message: 'Photo uploaded successfully',
        data: {
          photo_url: photoUrl,
          original_name: req.file.originalname,
          size: req.file.size,
          mime_type: req.file.mimetype
        },
      });
    } catch (error) {
      console.error('=== MULTIPART PHOTO UPLOAD ERROR ===');
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        type: typeof error,
        error
      });

      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: {
          type: 'INTERNAL_ERROR',
          details: 'Failed to upload photo',
        },
      });
    }
  }

  /**
   * Validate base64 image format
   */
  private static validateBase64Image(photoBase64: string): boolean {
    console.info('Validating base64 image:', {
      length: photoBase64.length,
      startsWithData: photoBase64.startsWith('data:'),
      preview: photoBase64.substring(0, 50)
    });

    // Check if it's a valid base64 image
    const base64Regex = /^data:image\/(jpeg|jpg|png|gif);base64,/;
    const isValid = base64Regex.test(photoBase64);

    console.info('Base64 validation result:', {
      isValid,
      regexPattern: base64Regex.toString()
    });

    return isValid;
  }
}
