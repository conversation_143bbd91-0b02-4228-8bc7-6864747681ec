import { PrismaClient } from "@prisma/client";

import { UserType } from "../types/models";
import { AppError } from "../utils/errors";

export class DashboardService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get dashboard data based on user type
   */
  async getDashboardData(
    userId: string,
    userType: UserType
  ): Promise<Record<string, any>> {
    try {
      switch (userType) {
        case UserType.CUSTOMER:
          return await this.getCustomerDashboardData(userId);
        case UserType.ACCESS_OPERATOR:
          return await this.getAccessOperatorDashboardData(userId);
        case UserType.CAR_OPERATOR:
          return await this.getCarOperatorDashboardData(userId);
        case UserType.ADMIN:
          return await this.getAdminDashboardData();
        default:
          throw AppError.badRequest("Invalid user type");
      }
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error getting dashboard data:", error);
      throw AppError.internal("Failed to get dashboard data");
    }
  }

  /**
   * Get customer dashboard data
   */
  private async getCustomerDashboardData(
    customerId: string
  ): Promise<Record<string, any>> {
    // Get shipment statistics
    const [
      totalShipments,
      pendingShipments,
      awaitingPickupShipments,
      inTransitShipments,
      arrivedShipments,
      deliveredShipments,
      cancelledShipments,
      recentShipments,
    ] = await Promise.all([
      this.prisma.shipment.count({ where: { customer_id: customerId } }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "PENDING" },
      }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "AWAITING_PICKUP" },
      }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "IN_TRANSIT" },
      }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "ARRIVED_AT_DESTINATION" },
      }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "DELIVERED" },
      }),
      this.prisma.shipment.count({
        where: { customer_id: customerId, status: "CANCELLED" },
      }),
      this.prisma.shipment.findMany({
        where: { customer_id: customerId },
        orderBy: { created_at: "desc" },
        take: 5,
        select: {
          id: true,
          tracking_code: true,
          status: true,
          description: true,
          created_at: true,
          estimated_delivery: true,
          originAO: {
            select: { business_name: true, address: true },
          },
          destAO: {
            select: { business_name: true, address: true },
          },
        },
      }),
    ]);

    return {
      shipment_stats: {
        total: totalShipments,
        pending: pendingShipments,
        awaiting_pickup: awaitingPickupShipments,
        in_transit: inTransitShipments,
        arrived_at_destination: arrivedShipments,
        delivered: deliveredShipments,
        cancelled: cancelledShipments,
      },
      recent_shipments: recentShipments,
      quick_actions: [
        {
          label: "Create New Shipment",
          action: "create_shipment",
          icon: "plus",
        },
        { label: "Track Shipment", action: "track_shipment", icon: "search" },
        { label: "View All Shipments", action: "view_shipments", icon: "list" },
      ],
    };
  }

  /**
   * Get access operator dashboard data
   */
  private async getAccessOperatorDashboardData(
    aoId: string
  ): Promise<Record<string, any>> {
    // Get access operator info
    // Note: aoId is the user_id, and AccessOperator.id = User.id (one-to-one relationship)
    const accessOperator = await this.prisma.accessOperator.findUnique({
      where: { id: aoId },
      select: {
        business_name: true,
        address: true,
        approved: true,
        geo_latitude: true,
        geo_longitude: true,
      },
    });

    if (!accessOperator) {
      console.error(`Access operator profile not found for user ID: ${aoId}`);
      // Return a dashboard indicating profile is incomplete
      return {
        profile_incomplete: true,
        business_info: {
          business_name: null,
          address: null,
          approved: false,
          has_location: false,
        },
        shipment_stats: {
          total: 0,
          pending: 0,
          awaiting_pickup: 0,
          in_transit: 0,
          arrived_at_destination: 0,
          delivered: 0,
        },
        qr_label_stats: {
          unused: 0,
          assigned: 0,
          total: 0,
        },
        recent_shipments: [],
        quick_actions: [
          {
            label: "Complete Profile",
            action: "complete_profile",
            icon: "user-plus",
            priority: true,
          },
          {
            label: "Update Business Info",
            action: "update_profile",
            icon: "edit",
          },
        ],
        message:
          "Please complete your business profile to access full dashboard features.",
      };
    }

    // Get shipment statistics for this AO
    const aoWhere = { OR: [{ origin_ao_id: aoId }, { dest_ao_id: aoId }] };
    const [
      totalShipments,
      pendingShipments,
      awaitingPickupShipments,
      inTransitShipments,
      arrivedShipments,
      deliveredShipments,
      recentShipments,
      unusedQRLabels,
      assignedQRLabels,
    ] = await Promise.all([
      this.prisma.shipment.count({ where: aoWhere }),
      this.prisma.shipment.count({ where: { ...aoWhere, status: "PENDING" } }),
      this.prisma.shipment.count({
        where: { ...aoWhere, status: "AWAITING_PICKUP" },
      }),
      this.prisma.shipment.count({
        where: { ...aoWhere, status: "IN_TRANSIT" },
      }),
      this.prisma.shipment.count({
        where: { ...aoWhere, status: "ARRIVED_AT_DESTINATION" },
      }),
      this.prisma.shipment.count({
        where: { ...aoWhere, status: "DELIVERED" },
      }),
      this.prisma.shipment.findMany({
        where: aoWhere,
        orderBy: { created_at: "desc" },
        take: 5,
        select: {
          id: true,
          tracking_code: true,
          status: true,
          description: true,
          created_at: true,
          customer: {
            select: { name: true, phone: true },
          },
        },
      }),
      this.prisma.shipmentQRLabel.count({
        where: { ao_id: aoId, status: "UNUSED" },
      }),
      this.prisma.shipmentQRLabel.count({
        where: { ao_id: aoId, status: "ASSIGNED" },
      }),
    ]);

    return {
      business_info: {
        business_name: accessOperator.business_name,
        address: accessOperator.address,
        approved: accessOperator.approved,
        has_location: !!(
          accessOperator.geo_latitude && accessOperator.geo_longitude
        ),
      },
      shipment_stats: {
        total: totalShipments,
        pending: pendingShipments,
        awaiting_pickup: awaitingPickupShipments,
        in_transit: inTransitShipments,
        arrived_at_destination: arrivedShipments,
        delivered: deliveredShipments,
      },
      qr_label_stats: {
        unused: unusedQRLabels,
        assigned: assignedQRLabels,
        total: unusedQRLabels + assignedQRLabels,
      },
      recent_shipments: recentShipments,
      quick_actions: [
        { label: "Generate QR Labels", action: "generate_qr", icon: "qrcode" },
        { label: "Scan Shipment", action: "scan_shipment", icon: "camera" },
        { label: "View All Shipments", action: "view_shipments", icon: "list" },
        {
          label: "Update Business Info",
          action: "update_profile",
          icon: "edit",
        },
      ],
    };
  }

  /**
   * Get car operator dashboard data
   */
  private async getCarOperatorDashboardData(
    coId: string
  ): Promise<Record<string, any>> {
    // Get car operator info
    // Note: coId is the user_id, and CarOperator.id = User.id (one-to-one relationship)
    const carOperator = await this.prisma.carOperator.findUnique({
      where: { id: coId },
      select: {
        license_number: true,
        vehicle_info: true,
        approved: true,
        pickup_access_point_id: true,
        dropoff_access_point_id: true,
        pickupAccessPoint: {
          select: { business_name: true, address: true },
        },
        dropoffAccessPoint: {
          select: { business_name: true, address: true },
        },
      },
    });

    if (!carOperator) {
      console.error(`Car operator profile not found for user ID: ${coId}`);
      // Return a dashboard indicating profile is incomplete
      return {
        profile_incomplete: true,
        operator_info: {
          license_number: null,
          vehicle_info: null,
          approved: false,
          pickup_access_point: null,
          dropoff_access_point: null,
        },
        shipment_stats: {
          assigned: 0,
          completed: 0,
          in_progress: 0,
          completion_rate: 0,
        },
        recent_shipments: [],
        available_shipments: [],
        quick_actions: [
          {
            label: "Complete Profile",
            action: "complete_profile",
            icon: "user-plus",
            priority: true,
          },
          {
            label: "Update Driver Info",
            action: "update_profile",
            icon: "edit",
          },
        ],
        message:
          "Please complete your driver profile to access shipments and full dashboard features.",
      };
    }

    // Get shipments assigned to this car operator
    const [assignedShipments, completedShipments, recentShipments] =
      await Promise.all([
        this.prisma.shipment.count({
          where: { assigned_car_operator_id: coId },
        }),
        this.prisma.shipment.count({
          where: {
            assigned_car_operator_id: coId,
            status: "DELIVERED",
          },
        }),
        this.prisma.shipment.findMany({
          where: { assigned_car_operator_id: coId },
          orderBy: { created_at: "desc" },
          take: 5,
          select: {
            id: true,
            tracking_code: true,
            status: true,
            description: true,
            created_at: true,
            originAO: {
              select: { business_name: true, address: true },
            },
            destAO: {
              select: { business_name: true, address: true },
            },
          },
        }),
      ]);

    return {
      operator_info: {
        license_number: carOperator.license_number,
        vehicle_info: carOperator.vehicle_info,
        approved: carOperator.approved,
        pickup_access_point: carOperator.pickupAccessPoint,
        dropoff_access_point: carOperator.dropoffAccessPoint,
        has_access_points: !!(
          carOperator.pickup_access_point_id &&
          carOperator.dropoff_access_point_id
        ),
      },
      shipment_stats: {
        assigned: assignedShipments,
        completed: completedShipments,
        completion_rate:
          assignedShipments > 0
            ? Math.round((completedShipments / assignedShipments) * 100)
            : 0,
      },
      recent_shipments: recentShipments,
      quick_actions: [
        {
          label: "View Assigned Shipments",
          action: "view_assigned",
          icon: "truck",
        },
        { label: "Scan Pickup", action: "scan_pickup", icon: "camera" },
        { label: "Scan Delivery", action: "scan_delivery", icon: "check" },
        {
          label: "Update Vehicle Info",
          action: "update_profile",
          icon: "edit",
        },
      ],
    };
  }

  /**
   * Get admin dashboard data
   */
  private async getAdminDashboardData(): Promise<Record<string, any>> {
    const [
      totalUsers,
      totalCustomers,
      totalAccessOperators,
      totalCarOperators,
      pendingApprovals,
      totalShipments,
      activeShipments,
      deliveredShipments,
      recentUsers,
      recentShipments,
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({ where: { user_type: UserType.CUSTOMER } }),
      this.prisma.user.count({
        where: { user_type: UserType.ACCESS_OPERATOR },
      }),
      this.prisma.user.count({ where: { user_type: UserType.CAR_OPERATOR } }),
      Promise.all([
        this.prisma.accessOperator.count({ where: { approved: false } }),
        this.prisma.carOperator.count({ where: { approved: false } }),
      ]).then(([aoCount, coCount]) => aoCount + coCount),
      this.prisma.shipment.count(),
      this.prisma.shipment.count({
        where: {
          status: {
            in: [
              "PENDING",
              "AWAITING_PICKUP",
              "IN_TRANSIT",
              "ARRIVED_AT_DESTINATION",
            ],
          },
        },
      }),
      this.prisma.shipment.count({ where: { status: "DELIVERED" } }),
      this.prisma.user.findMany({
        orderBy: { created_at: "desc" },
        take: 5,
        select: {
          id: true,
          name: true,
          email: true,
          user_type: true,
          status: true,
          created_at: true,
        },
      }),
      this.prisma.shipment.findMany({
        orderBy: { created_at: "desc" },
        take: 5,
        select: {
          id: true,
          tracking_code: true,
          status: true,
          description: true,
          created_at: true,
          customer: {
            select: { name: true },
          },
        },
      }),
    ]);

    return {
      user_stats: {
        total: totalUsers,
        customers: totalCustomers,
        access_operators: totalAccessOperators,
        car_operators: totalCarOperators,
        pending_approvals: pendingApprovals,
      },
      shipment_stats: {
        total: totalShipments,
        active: activeShipments,
        delivered: deliveredShipments,
        delivery_rate:
          totalShipments > 0
            ? Math.round((deliveredShipments / totalShipments) * 100)
            : 0,
      },
      recent_users: recentUsers,
      recent_shipments: recentShipments,
      quick_actions: [
        { label: "Manage Users", action: "manage_users", icon: "users" },
        {
          label: "Approve Operators",
          action: "approve_operators",
          icon: "check-circle",
        },
        { label: "View All Shipments", action: "view_shipments", icon: "list" },
        { label: "System Statistics", action: "view_stats", icon: "chart-bar" },
      ],
    };
  }
}
