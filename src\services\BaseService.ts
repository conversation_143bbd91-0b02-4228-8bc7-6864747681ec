/* eslint-disable no-unused-vars */
import { PrismaClient } from '@prisma/client';

/**
 * Base service class that provides common functionality for all services
 * including automatic tracking of who made updates
 */
export abstract class BaseService {
  protected prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Adds updated_by field to update data if updatedBy is provided
   * @param updateData - The data to update
   * @param updatedBy - ID of the user making the update
   * @returns Updated data with updated_by field if applicable
   */
  protected addUpdatedBy<T extends Record<string, any>>(
    updateData: T,
    updatedBy?: string
  ): T & { updated_by?: string } {
    if (updatedBy) {
      return {
        ...updateData,
        updated_by: updatedBy,
      };
    }
    return updateData;
  }

  /**
   * Creates audit log entry for tracking changes
   * @param userId - ID of the user making the change
   * @param action - Action being performed
   * @param entityId - ID of the entity being changed (optional)
   * @param details - Additional details about the change
   * @param adminId - ID of admin making the change (optional)
   */
  protected async createAuditLog(
    userId: string | undefined,
    action: string,
    entityId?: string,
    details: Record<string, any> = {},
    adminId?: string
  ): Promise<void> {
    const data: any = {
      action,
      details,
    };

    if (userId) {
      data.user_id = userId;
    }

    if (adminId) {
      data.admin_id = adminId;
    }

    if (entityId) {
      data.shipment_id = entityId; // For now, using shipment_id for all entities
    }

    await this.prisma.auditLog.create({ data });
  }

  /**
   * Validates that a user exists and is active
   * @param userId - ID of the user to validate
   * @returns True if user exists and is active
   */
  protected async validateUser(userId: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, status: true },
    });

    return user !== null && user.status === 'ACTIVE';
  }

  /**
   * Gets the current timestamp for consistent date handling
   * @returns Current timestamp
   */
  protected getCurrentTimestamp(): Date {
    return new Date();
  }

  /**
   * Wraps database operations in a transaction with automatic error handling
   * @param operation - The database operation to perform
   * @returns Result of the operation
   */
  protected async withTransaction<T>(
    operation: (tx: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>
  ): Promise<T> {
    return await this.prisma.$transaction(operation);
  }

  /**
   * Standardizes error handling across services
   * @param error - The error to handle
   * @param context - Additional context about where the error occurred
   */
  protected handleError(error: any, context: string): never {
    console.error(`Error in ${context}:`, error);
    
    if (error.code === 'P2002') {
      throw new Error('Unique constraint violation');
    }
    
    if (error.code === 'P2025') {
      throw new Error('Record not found');
    }
    
    if (error.code === 'P2003') {
      throw new Error('Foreign key constraint violation');
    }
    
    throw error;
  }
}
