/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import { PrismaClient } from "@prisma/client";

import { ServiceFactory } from "./ServiceFactory";
import { ShipmentNotificationService } from "./ShipmentNotificationService";
import { IAuditLogService, INotificationService } from "../interfaces/services";
import {
  CancellationReason,
  ShipmentStatus,
  NotificationType,
} from "../types/models";

/**
 * Service for handling shipment status changes with all required logging and notifications
 */
export class ShipmentStatusService {
  private prisma: PrismaClient;
  private auditLogService: IAuditLogService;
  private notificationService: INotificationService;
  private shipmentNotificationService?: ShipmentNotificationService;
  private emailService: any;

  constructor(
    prisma: PrismaClient,
    auditLogService: IAuditLogService,
    notificationService: INotificationService,
    shipmentNotificationService?: ShipmentNotificationService
  ) {
    this.prisma = prisma;
    this.auditLogService = auditLogService;
    this.notificationService = notificationService;
    this.shipmentNotificationService = shipmentNotificationService;
    this.emailService = ServiceFactory.getEmailService();
  }

  /**
   * Update shipment status with all required logging and notifications in a single transaction
   *
   * @param shipmentId - ID of the shipment to update
   * @param newStatus - New status to set
   * @param changedById - ID of the user making the change
   * @param notes - Optional notes about the status change
   * @returns The updated shipment
   */
  async updateShipmentStatus(
    shipmentId: string,
    newStatus: ShipmentStatus,
    changedById: string,
    notes?: string
  ) {
    // Get the current shipment to check its status and get interested parties
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        customer: true,
        originAO: { include: { user: true } },
        destAO: { include: { user: true } },
      },
    });

    if (!shipment) {
      throw new Error(`Shipment with ID ${shipmentId} not found`);
    }

    const oldStatus = shipment.status;

    // If status is already the same, no need to update
    if (oldStatus === newStatus) {
      return shipment;
    }

    // Perform all operations in a transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // 1. Update the Shipment status
      const updatedShipment = await tx.shipment.update({
        where: { id: shipmentId },
        data: { status: newStatus },
      });

      // 2. Add a record in ShipmentStatusHistory
      await tx.shipmentStatusHistory.create({
        data: {
          shipment_id: shipmentId,
          status: newStatus,
          changed_by_id: changedById,
          notes,
        },
      });

      // 3. Add a record in AuditLog
      await tx.auditLog.create({
        data: {
          user_id: changedById,
          shipment_id: shipmentId,
          action: "UPDATE_STATUS",
          details: {
            from: oldStatus,
            to: newStatus,
            notes,
          },
        },
      });

      return updatedShipment;
    });

    // 4. Send comprehensive notifications (outside transaction to avoid blocking)
    try {
      const notificationService = this.shipmentNotificationService;
      if (notificationService) {
        const context = await notificationService.getShipmentContext(
          shipmentId
        );
        if (context) {
          await notificationService.notifyStatusChange(
            context,
            oldStatus,
            newStatus
          );
        }
      }
    } catch (error) {
      console.error("Failed to send status change notifications:", error);
      // Don't throw error as the main operation succeeded
    }

    // 5. Send Email Notifications (outside transaction to avoid blocking)
    try {
      await this.sendStatusChangeEmails(shipment, newStatus);
    } catch (error) {
      console.error("Failed to send status change emails:", error);
      // Don't throw error as the main operation succeeded
    }

    return result;
  }

  /**
   * Get all users who should be notified about a shipment status change
   */
  private getInterestedParties(shipment: any): string[] {
    const parties = new Set<string>();

    // Always include the customer
    parties.add(shipment.customer_id);

    // Include origin AO if exists
    if (shipment.originAO?.user?.id) {
      parties.add(shipment.originAO.user.id);
    }

    // Include destination AO if exists
    if (shipment.destAO?.user?.id) {
      parties.add(shipment.destAO.user.id);
    }

    // TODO: Include any car operators assigned to this shipment
    // This would require additional query to find relevant COs

    return Array.from(parties);
  }

  /**
   * Update shipment status with cancellation reason for cancelled shipments
   */
  async updateShipmentStatusWithReason(
    shipmentId: string,
    newStatus: ShipmentStatus,
    changedById: string | null,
    cancellationReason?: CancellationReason,
    notes?: string
  ) {
    // Get the current shipment to check its status and get interested parties
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        customer: true,
        originAO: { include: { user: true } },
        destAO: { include: { user: true } },
      },
    });

    if (!shipment) {
      throw new Error(`Shipment with ID ${shipmentId} not found`);
    }

    const oldStatus = shipment.status;

    // If status is already the same, no need to update
    if (oldStatus === newStatus) {
      return shipment;
    }

    // Perform all operations in a transaction
    return await this.prisma.$transaction(async (tx) => {
      // 1. Update the Shipment status with cancellation details if applicable
      const updateData: any = { status: newStatus };

      if (newStatus === ShipmentStatus.CANCELLED) {
        updateData.cancellation_reason = cancellationReason;
        updateData.cancelled_at = new Date();
      }

      const updatedShipment = await tx.shipment.update({
        where: { id: shipmentId },
        data: updateData,
      });

      // 2. Add a record in ShipmentStatusHistory
      await tx.shipmentStatusHistory.create({
        data: {
          shipment_id: shipmentId,
          status: newStatus,
          changed_by_id: changedById,
          notes,
        },
      });

      // 3. Add a record in AuditLog
      const auditDetails: any = {
        from: oldStatus,
        to: newStatus,
        notes,
      };

      if (cancellationReason) {
        auditDetails.cancellation_reason = cancellationReason;
      }

      await tx.auditLog.create({
        data: {
          user_id: changedById,
          shipment_id: shipmentId,
          action: "UPDATE_STATUS",
          details: auditDetails,
        },
      });

      return updatedShipment;
    });
  }

  /**
   * Send email notifications for status changes
   */
  private async sendStatusChangeEmails(
    shipment: any,
    newStatus: ShipmentStatus
  ): Promise<void> {
    try {
      // Get additional info for email templates
      const additionalInfo = await this.getEmailAdditionalInfo(
        shipment,
        newStatus
      );

      // Send email to customer
      if (shipment.customer?.email) {
        await this.emailService.sendShipmentStatusEmail(
          shipment.customer.email,
          shipment.customer.name,
          shipment.id,
          newStatus,
          shipment.tracking_code,
          additionalInfo
        );
      }

      // Send email to origin AO for relevant statuses
      if (
        shipment.originAO?.user?.email &&
        this.shouldNotifyOriginAO(newStatus)
      ) {
        await this.emailService.sendShipmentStatusEmail(
          shipment.originAO.user.email,
          shipment.originAO.user.name,
          shipment.id,
          newStatus,
          shipment.tracking_code,
          { ...additionalInfo, isAO: true, aoType: "origin" }
        );
      }

      // Send email to destination AO for relevant statuses
      if (
        shipment.destAO?.user?.email &&
        this.shouldNotifyDestinationAO(newStatus)
      ) {
        await this.emailService.sendShipmentStatusEmail(
          shipment.destAO.user.email,
          shipment.destAO.user.name,
          shipment.id,
          newStatus,
          shipment.tracking_code,
          { ...additionalInfo, isAO: true, aoType: "destination" }
        );
      }

      // Send email to car operator if assigned
      if (shipment.assigned_car_operator_id) {
        const carOperator = await this.prisma.carOperator.findUnique({
          where: { id: shipment.assigned_car_operator_id },
          include: { user: true },
        });

        if (
          carOperator?.user?.email &&
          this.shouldNotifyCarOperator(newStatus)
        ) {
          await this.emailService.sendShipmentStatusEmail(
            carOperator.user.email,
            carOperator.user.name,
            shipment.id,
            newStatus,
            shipment.tracking_code,
            { ...additionalInfo, isCO: true }
          );
        }
      }
    } catch (error) {
      console.error("Error sending status change emails:", error);
      throw error;
    }
  }

  /**
   * Get additional information for email templates
   */
  private async getEmailAdditionalInfo(
    shipment: any,
    status: ShipmentStatus
  ): Promise<any> {
    const info: any = {
      pickupCode: shipment.pickup_code,
      originAO: shipment.originAO?.business_name || "Origin Access Point",
      destinationAO:
        shipment.destAO?.business_name || "Destination Access Point",
      destinationAddress: shipment.destAO?.address,
    };

    // Add car operator info if assigned
    if (shipment.assigned_car_operator_id) {
      const carOperator = await this.prisma.carOperator.findUnique({
        where: { id: shipment.assigned_car_operator_id },
        include: { user: true },
      });
      if (carOperator) {
        info.carOperatorName = carOperator.user.name;
      }
    }

    // Add delivery timestamp for delivered status
    if (status === ShipmentStatus.DELIVERED) {
      info.deliveredAt = new Date().toLocaleString();
    }

    return info;
  }

  /**
   * Determine if origin AO should be notified for this status
   */
  private shouldNotifyOriginAO(status: ShipmentStatus): boolean {
    return [
      ShipmentStatus.AWAITING_PICKUP,
      ShipmentStatus.PICKED_UP_BY_CO,
      ShipmentStatus.CANCELLED,
    ].includes(status);
  }

  /**
   * Determine if destination AO should be notified for this status
   */
  private shouldNotifyDestinationAO(status: ShipmentStatus): boolean {
    return [
      ShipmentStatus.IN_TRANSIT,
      ShipmentStatus.ARRIVED_AT_DESTINATION,
      ShipmentStatus.READY_FOR_DELIVERY,
      ShipmentStatus.DELIVERED,
      ShipmentStatus.CANCELLED,
    ].includes(status);
  }

  /**
   * Determine if car operator should be notified for this status
   */
  private shouldNotifyCarOperator(status: ShipmentStatus): boolean {
    return [
      ShipmentStatus.AWAITING_PICKUP,
      ShipmentStatus.PICKED_UP_BY_CO,
      ShipmentStatus.IN_TRANSIT,
      ShipmentStatus.ARRIVED_AT_DESTINATION,
      ShipmentStatus.DELIVERED,
      ShipmentStatus.CANCELLED,
    ].includes(status);
  }

  /**
   * Get appropriate notification message based on status change
   */
  private getStatusChangeMessage(
    oldStatus: ShipmentStatus,
    newStatus: ShipmentStatus,
    cancellationReason?: CancellationReason
  ): string {
    switch (newStatus) {
      case ShipmentStatus.PENDING:
        return "Your shipment has been created and is pending drop-off. You have 24 hours to drop off your package.";
      case ShipmentStatus.AWAITING_PICKUP:
        return "Your package has been dropped off and is awaiting car operator pickup. The 24-hour timer has been stopped.";
      case ShipmentStatus.PICKED_UP_BY_CO:
        return "Your package has been picked up by a car operator and is now in transit.";
      case ShipmentStatus.IN_TRANSIT:
        return "Your package is currently in transit to the destination access point.";
      case ShipmentStatus.ARRIVED_AT_DESTINATION:
        return "Your package has arrived at the destination access point.";
      case ShipmentStatus.READY_FOR_DELIVERY:
        return "Your package is ready for pickup at the destination access point.";
      case ShipmentStatus.DELIVERED:
        return "Your shipment has been delivered successfully.";
      case ShipmentStatus.CANCELLED:
        if (cancellationReason === CancellationReason.SYSTEM_EXPIRED) {
          return "Your shipment has been automatically cancelled due to 24-hour expiry.";
        } else if (cancellationReason === CancellationReason.USER_CANCELLED) {
          return "Your shipment has been cancelled by you.";
        } else if (cancellationReason === CancellationReason.ADMIN_CANCELLED) {
          return "Your shipment has been cancelled by an administrator.";
        }
        return "Your shipment has been cancelled.";
      default:
        return `Your shipment status has changed from ${oldStatus} to ${newStatus}.`;
    }
  }
}
