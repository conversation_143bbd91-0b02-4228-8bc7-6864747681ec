import fs from 'fs';
import path from 'path';

import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

import { S3_CONFIG } from '../data/constant';
import { s3Service } from '../services/S3Service';

// Ensure upload directories exist
const createUploadDirectories = () => {
  const uploadDir = path.join(process.cwd(), 'uploads');
  const photoDir = path.join(uploadDir, 'photos');

  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir);
  }

  if (!fs.existsSync(photoDir)) {
    fs.mkdirSync(photoDir);
  }
};

// Create directories on startup
createUploadDirectories();

// Configure storage based on environment
const storage = s3Service.isConfigured()
  ? multer.memoryStorage() // Use memory storage when S3 is available
  : multer.diskStorage({
      destination: (_req: Request, _file: Express.Multer.File, cb) => {
        const uploadPath = path.join(process.cwd(), 'uploads', 'photos');
        cb(null, uploadPath);
      },
      filename: (_req: Request, file: Express.Multer.File, cb) => {
        // Generate a unique filename with original extension
        const fileExt = path.extname(file.originalname);
        const fileName = `${uuidv4()}${fileExt}`;
        cb(null, fileName);
      },
    });

// File filter to only allow image files
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

// Create multer upload instance
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
});

// Export the upload middleware
export const uploadPhoto = (req: Request, res: Response, next: NextFunction) => {
  console.info('[MIDDLEWARE] uploadPhoto', req.method, req.originalUrl);
  return upload.single('photo')(req, res, next);
};

// Middleware for handling S3 uploads after multer processing
export const uploadToS3 = async (req: Request, _res: Response, next: NextFunction) => {
  try {
    // Only process if we have a file and S3 is configured
    if (!req.file || !s3Service.isConfigured()) {
      return next();
    }

    console.info('[MIDDLEWARE] uploadToS3 - Processing file upload to S3');

    // Generate unique filename with original extension
    const fileExt = path.extname(req.file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;

    // Determine folder based on request or default to general
    const folder = (req.body.folder as keyof typeof S3_CONFIG.folders) || 'general';

    // Upload to S3
    const photoUrl = await s3Service.uploadPhotoBuffer(
      req.file.buffer,
      fileName,
      folder,
      req.file.mimetype
    );

    // Add the S3 URL to the request for the controller to use
    req.body.s3_photo_url = photoUrl;

    console.info('[MIDDLEWARE] uploadToS3 - Successfully uploaded to S3:', photoUrl);
    next();
  } catch (error) {
    console.error('[MIDDLEWARE] uploadToS3 - Error uploading to S3:', error);
    next(error);
  }
};

// Helper function to get file URL based on environment
export const getFileUrl = (filename: string, folder: string = 'general'): string => {
  if (s3Service.isConfigured()) {
    // Return S3 URL when S3 is configured
    return `https://${S3_CONFIG.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${S3_CONFIG.folders[folder as keyof typeof S3_CONFIG.folders] || S3_CONFIG.folders.general}/${filename}`;
  } else {
    // Return local path when S3 is not configured
    return `/uploads/photos/${folder}/${filename}`;
  }
};
