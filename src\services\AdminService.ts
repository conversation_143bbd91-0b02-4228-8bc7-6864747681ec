/* eslint-disable @typescript-eslint/no-unused-vars */
import crypto from "crypto";

import { PrismaClient, Admin, AdminRole, UserStatus } from "@prisma/client";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";

import { IAdminService } from "../interfaces/IAdminService";
import { IEmailService } from "../interfaces/IEmailService";
import { IAuditLogService } from "../interfaces/services";
import { AppError } from "../utils/errors";

export class AdminService implements IAdminService {
  private prisma: PrismaClient;
  private emailService: IEmailService;
  private auditLogService: IAuditLogService;

  constructor(
    prisma: PrismaClient,
    emailService: IEmailService,
    auditLogService: IAuditLogService
  ) {
    this.prisma = prisma;
    this.emailService = emailService;
    this.auditLogService = auditLogService;
  }

  /**
   * Register a new admin
   */
  async registerAdmin(
    name: string,
    email: string,
    password: string,
    role: AdminRole = AdminRole.ADMIN
  ): Promise<Admin> {
    try {
      // Check if admin with email already exists
      const existingAdmin = await this.prisma.admin.findUnique({
        where: { email },
      });

      if (existingAdmin) {
        throw AppError.badRequest("Admin with this email already exists");
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create admin with email_verified set to false
      const admin = await this.prisma.admin.create({
        data: {
          name,
          email,
          password_hash: hashedPassword,
          role,
          status: UserStatus.ACTIVE,
          email_verified: false,
        },
      });

      // Generate OTP (6-digit number)
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create admin verification record
      await this.prisma.adminVerification.create({
        data: {
          admin_id: admin.id,
          otp,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_REGISTERED",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );

      // Send OTP email
      try {
        await this.emailService.sendAdminOTPEmail(admin.email, admin.name, otp);
      } catch (emailError) {
        console.error(
          `Failed to send OTP email to admin: ${admin.email}`,
          emailError
        );
        // Continue with registration even if email fails
      }

      // Return admin without password
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = admin;
      return adminWithoutPassword as Admin;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error registering admin:", error);
      throw AppError.internal("Failed to register admin");
    }
  }

  /**
   * Verify admin OTP
   */
  async verifyAdminOTP(email: string, otp: string): Promise<Admin> {
    try {
      // Find admin by email
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      // Check if admin exists
      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Check if admin is already verified
      if (admin.email_verified) {
        throw AppError.conflict("Email is already verified");
      }

      // Find the verification record
      const verification = await this.prisma.adminVerification.findFirst({
        where: {
          admin_id: admin.id,
          otp,
          used: false,
          expires_at: {
            gt: new Date(), // OTP must not be expired
          },
        },
      });

      if (!verification) {
        throw AppError.badRequest("Invalid or expired OTP");
      }

      // Mark the verification token as used
      await this.prisma.adminVerification.update({
        where: { id: verification.id },
        data: { used: true },
      });

      // Update admin
      const updatedAdmin = await this.prisma.admin.update({
        where: { id: admin.id },
        data: {
          email_verified: true,
        },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_EMAIL_VERIFIED",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );

      // Send welcome email
      await this.emailService.sendAdminWelcomeEmail(
        admin.email,
        admin.name,
        admin.role
      );

      return updatedAdmin as Admin;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error verifying admin OTP:", error);
      throw AppError.internal("Failed to verify OTP");
    }
  }

  /**
   * Resend admin OTP
   */
  async resendAdminOTP(email: string): Promise<void> {
    try {
      // Find admin by email
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      // Check if admin exists
      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Check if admin is already verified
      if (admin.email_verified) {
        throw AppError.conflict("Email is already verified");
      }

      // Generate OTP (6-digit number)
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Create admin verification record
      await this.prisma.adminVerification.create({
        data: {
          admin_id: admin.id,
          otp,
          expires_at: expiresAt,
          used: false,
        },
      });

      // Send OTP email
      await this.emailService.sendAdminOTPEmail(admin.email, admin.name, otp);

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_OTP_RESENT",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error resending admin OTP:", error);
      throw AppError.internal("Failed to resend OTP");
    }
  }

  /**
   * Login admin
   */
  async loginAdmin(
    email: string,
    password: string,
    ipAddress: string
  ): Promise<{ admin: Admin; token: string }> {
    try {
      // Find admin by email
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      if (!admin) {
        // Log failed login attempt
        await this.prisma.loginAttempt.create({
          data: {
            ip_address: ipAddress,
            successful: false,
            device: "Admin Portal",
          },
        });
        throw AppError.unauthorized("Invalid email or password");
      }

      // Check if admin is active
      if (admin.status !== UserStatus.ACTIVE) {
        await this.prisma.loginAttempt.create({
          data: {
            admin_id: admin.id,
            ip_address: ipAddress,
            successful: false,
            device: "Admin Portal",
          },
        });
        throw AppError.unauthorized("Admin account is not active");
      }

      // Skip email verification check for simplified admin flow

      // Verify password
      const isPasswordValid = await bcrypt.compare(
        password,
        admin.password_hash
      );
      if (!isPasswordValid) {
        // Log failed login attempt
        await this.prisma.loginAttempt.create({
          data: {
            admin_id: admin.id,
            ip_address: ipAddress,
            successful: false,
            device: "Admin Portal",
          },
        });
        throw AppError.unauthorized("Invalid email or password");
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          id: admin.id,
          email: admin.email,
          role: admin.role,
          isAdmin: true,
        },
        process.env.JWT_SECRET || "your-secret-key",
        { expiresIn: "8h" }
      );

      // Log successful login
      await this.prisma.loginAttempt.create({
        data: {
          admin_id: admin.id,
          ip_address: ipAddress,
          successful: true,
          device: "Admin Portal",
        },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_LOGIN",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );

      // Return admin without password and token
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = admin;
      return {
        admin: adminWithoutPassword as Admin,
        token,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error logging in admin:", error);
      throw AppError.internal("Failed to login");
    }
  }

  /**
   * Get admin by ID
   */
  async getAdminById(id: string): Promise<Admin | null> {
    try {
      const admin = await this.prisma.admin.findUnique({
        where: { id },
      });

      if (!admin) return null;

      // Return admin without password
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = admin;
      return adminWithoutPassword as Admin;
    } catch (error) {
      console.error("Error getting admin by ID:", error);
      throw AppError.internal("Failed to get admin");
    }
  }

  /**
   * Get admin by email
   */
  async getAdminByEmail(email: string): Promise<Admin | null> {
    try {
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      if (!admin) return null;

      // Return admin without password
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = admin;
      return adminWithoutPassword as Admin;
    } catch (error) {
      console.error("Error getting admin by email:", error);
      throw AppError.internal("Failed to get admin");
    }
  }

  /**
   * Update admin
   */
  async updateAdmin(
    id: string,
    adminData: { name?: string; role?: AdminRole }
  ): Promise<Admin> {
    try {
      // Check if admin exists
      const existingAdmin = await this.prisma.admin.findUnique({
        where: { id },
      });

      if (!existingAdmin) {
        throw AppError.notFound("Admin not found");
      }

      // Prepare data for update
      const updateData: any = {};
      if (adminData.name) updateData.name = adminData.name;
      if (adminData.role) updateData.role = adminData.role;

      // Update admin
      const updatedAdmin = await this.prisma.admin.update({
        where: { id },
        data: updateData,
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_UPDATED",
        undefined,
        {
          admin_id: updatedAdmin.id,
          email: updatedAdmin.email,
        },
        id
      );

      // Return admin without password
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = updatedAdmin;
      return adminWithoutPassword as Admin;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error updating admin:", error);
      throw AppError.internal("Failed to update admin");
    }
  }

  /**
   * Change admin password
   */
  async changePassword(
    adminId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Validate inputs
      if (!adminId || !currentPassword || !newPassword) {
        throw AppError.badRequest(
          "Admin ID, current password, and new password are required"
        );
      }

      // Get admin by ID
      const admin = await this.prisma.admin.findUnique({
        where: { id: adminId },
      });

      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(
        currentPassword,
        admin.password_hash
      );
      if (!isPasswordValid) {
        throw AppError.badRequest("Current password is incorrect");
      }

      // Check if new password is the same as current password
      if (currentPassword === newPassword) {
        throw AppError.badRequest(
          "New password must be different from current password"
        );
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update admin password
      await this.prisma.admin.update({
        where: { id: adminId },
        data: {
          password_hash: hashedPassword,
        },
      });

      // Send password changed confirmation email
      await this.emailService.sendAdminPasswordChangedEmail(
        admin.email,
        admin.name
      );

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_PASSWORD_CHANGED",
        undefined,
        {
          admin_id: adminId,
          email: admin.email,
        },
        adminId
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error changing admin password:", error);
      throw AppError.internal("Failed to change password");
    }
  }

  /**
   * Forgot password - send reset token
   */
  async forgotPassword(email: string): Promise<void> {
    try {
      // Find admin by email
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      if (!admin) {
        // Don't reveal that the admin doesn't exist
        return;
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString("hex");

      // Set expiration time (5 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 5);

      // Store token in database
      // Note: We need to create a table for admin password resets
      // For now, we'll use the security events table
      await this.prisma.securityEvent.create({
        data: {
          admin_id: admin.id,
          event_type: "PASSWORD_RESET_REQUESTED",
          details: {
            token: resetToken,
            expires_at: expiresAt,
            used: false,
          },
        },
      });

      // Generate reset link
      const serverUrl = process.env.SERVER_URL || "http://localhost:8000";
      const resetLink = `${serverUrl}/api/admin/reset-password?token=${resetToken}&email=${encodeURIComponent(
        email
      )}`;

      // Send reset email
      await this.emailService.sendAdminPasswordResetEmail(
        email,
        admin.name,
        resetLink
      );

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_PASSWORD_RESET_REQUESTED",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );
    } catch (error) {
      console.error("Error in admin forgot password:", error);
      throw AppError.internal("Failed to process password reset request");
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(
    email: string,
    token: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Find admin by email
      const admin = await this.prisma.admin.findUnique({
        where: { email },
      });

      if (!admin) {
        throw AppError.badRequest("Invalid reset request");
      }

      // Find the reset token in security events
      const securityEvent = await this.prisma.securityEvent.findFirst({
        where: {
          admin_id: admin.id,
          event_type: "PASSWORD_RESET_REQUESTED",
          details: {
            path: ["token"],
            equals: token,
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      if (!securityEvent) {
        throw AppError.badRequest("Invalid or expired reset token");
      }

      // Check if token is used or expired
      const details = securityEvent.details as any;
      if (details.used) {
        throw AppError.badRequest("Reset token has already been used");
      }

      const expiresAt = new Date(details.expires_at);
      if (expiresAt < new Date()) {
        throw AppError.badRequest("Reset token has expired");
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update admin password
      await this.prisma.admin.update({
        where: { id: admin.id },
        data: {
          password_hash: hashedPassword,
        },
      });

      // Mark token as used
      await this.prisma.securityEvent.create({
        data: {
          admin_id: admin.id,
          event_type: "PASSWORD_RESET_COMPLETED",
          details: {
            token_used: token,
          },
        },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_PASSWORD_RESET_COMPLETED",
        undefined,
        {
          admin_id: admin.id,
          email: admin.email,
        },
        admin.id
      );
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error resetting admin password:", error);
      throw AppError.internal("Failed to reset password");
    }
  }

  /**
   * Get all admins with pagination
   */
  async getAllAdmins(options?: {
    skip?: number;
    take?: number;
    search?: string;
    role?: AdminRole;
    status?: UserStatus;
  }): Promise<{ admins: Admin[]; total: number }> {
    try {
      const { skip = 0, take = 10, search, role, status } = options || {};

      // Build where conditions
      const where: any = {};

      // Add search condition if provided
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { email: { contains: search, mode: "insensitive" } },
        ];
      }

      // Add role filter if provided
      if (role) {
        where.role = role;
      }

      // Add status filter if provided
      if (status) {
        where.status = status;
      }

      // Get total count for pagination
      const total = await this.prisma.admin.count({ where });

      // Get admins with pagination
      const admins = await this.prisma.admin.findMany({
        where,
        orderBy: {
          created_at: "desc",
        },
        skip,
        take,
      });

      // Remove password_hash from each admin
      const adminsWithoutPassword = admins.map((admin) => {
        // eslint-disable-next-line no-unused-vars
        const { password_hash, ...adminWithoutPassword } = admin;
        return adminWithoutPassword as Admin;
      });

      return { admins: adminsWithoutPassword, total };
    } catch (error) {
      console.error("Error getting all admins:", error);
      throw AppError.internal("Failed to get admins");
    }
  }

  /**
   * Change admin status (activate/deactivate)
   */
  async changeAdminStatus(id: string, status: UserStatus): Promise<Admin> {
    try {
      // Check if admin exists
      const existingAdmin = await this.prisma.admin.findUnique({
        where: { id },
      });

      if (!existingAdmin) {
        throw AppError.notFound("Admin not found");
      }

      // Update admin status
      const updatedAdmin = await this.prisma.admin.update({
        where: { id },
        data: { status },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_STATUS_CHANGED",
        undefined,
        {
          admin_id: updatedAdmin.id,
          email: updatedAdmin.email,
          status,
        },
        id
      );

      // Return admin without password
      // eslint-disable-next-line no-unused-vars
      const { password_hash, ...adminWithoutPassword } = updatedAdmin;
      return adminWithoutPassword as Admin;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error changing admin status:", error);
      throw AppError.internal("Failed to change admin status");
    }
  }

  /**
   * Logout admin
   * This method logs the logout event and can be extended to invalidate tokens if needed
   */
  async logoutAdmin(adminId: string): Promise<void> {
    try {
      // Validate admin ID
      if (!adminId) {
        throw AppError.badRequest("Admin ID is required");
      }

      // Check if admin exists
      const admin = await this.getAdminById(adminId);
      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Log the logout action
      await this.auditLogService.createAuditLog(
        undefined,
        "ADMIN_LOGOUT",
        undefined,
        {
          admin_id: adminId,
          email: admin.email,
        },
        adminId
      );

      // In a more advanced implementation, we could add the token to a blacklist
      // or invalidate all sessions for this admin
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error logging out admin:", error);
      throw AppError.internal("Failed to logout admin");
    }
  }

  /**
   * Change user status (activate/deactivate)
   */
  async changeUserStatus(userId: string, status: UserStatus, adminId?: string): Promise<any> {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        throw AppError.notFound("User not found");
      }

      // Update user status
      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { status },
      });

      // Log the action
      await this.auditLogService.createAuditLog(
        undefined,
        "USER_STATUS_CHANGED_BY_ADMIN",
        undefined,
        {
          user_id: updatedUser.id,
          status,
        },
        adminId
      );

      return updatedUser;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error changing user status:", error);
      throw AppError.internal("Failed to change user status");
    }
  }
}
