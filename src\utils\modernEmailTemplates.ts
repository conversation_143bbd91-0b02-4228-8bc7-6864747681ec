/**
 * Modern Email Templates
 *
 * This file contains modern email templates with professional design,
 * using Cairo font for Arabic and Roboto Condensed for English.
 */

/**
 * Modern shipment status update notification (English content)
 * @param name Recipient's name
 * @param shipmentId Shipment ID
 * @param status Current status
 * @param trackingCode Tracking code
 * @param details Additional details
 * @returns English content for modern email
 */
export function modernShipmentUpdateEnglishContent(
  name: string,
  shipmentId: string,
  status: string,
  trackingCode: string,
  details?: any
): string {
  const currentDate = new Date().toLocaleDateString('en-US');
  
  return `
    <div style="margin-bottom: 20px;">
      <h2 style="color: #4CAF50; margin: 0 0 10px 0; font-size: 20px; font-weight: 700;">
        SHIPMENT STATUS UPDATE
      </h2>
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        ${currentDate}
      </p>
    </div>

    <div style="background-color: #f3fff3; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50; margin: 20px 0;">
      <h3 style="color: #4CAF50; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Hello ${name},
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Your shipment status has been updated. We're sending you this notification whenever your shipment status changes.
      </p>
    </div>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
        UPDATED SHIPMENT INFORMATION
      </h4>
      
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
            Shipment ID:
          </td>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
            ${shipmentId}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
            Tracking Code:
          </td>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
            ${trackingCode}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
            Current Status:
          </td>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
            <span style="background-color: #2196F3; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase;">
              ${status.replace('_', ' ')}
            </span>
          </td>
        </tr>
        ${details ? `
        <tr>
          <td style="padding: 8px 0; font-weight: 600; color: #555;">
            Additional Details:
          </td>
          <td style="padding: 8px 0; color: #333;">
            ${details}
          </td>
        </tr>
        ` : ''}
      </table>
    </div>

    <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50; margin: 20px 0;">
      <p style="margin: 0; color: #2e7d32; font-size: 14px; line-height: 1.5;">
        <strong>Tip:</strong> You can track your shipment anytime using the tracking code mentioned above.
      </p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px;">
        Thank you for using Shipment Relay Platform
      </p>
      <p style="margin: 5px 0; color: #888; font-size: 12px;">
        Shipment Relay Team
      </p>
    </div>
  `;
}

/**
 * Modern shipment created notification (English content)
 * @param name Customer's name
 * @param shipmentId Shipment ID
 * @param pickupCode Pickup code
 * @param trackingCode Tracking code
 * @returns English content for modern email
 */
export function modernShipmentCreatedEnglishContent(
  name: string,
  shipmentId: string,
  pickupCode: string,
  trackingCode: string
): string {
  const currentDate = new Date().toLocaleDateString('en-US');
  
  return `
    <div style="margin-bottom: 20px;">
      <h2 style="color: #4CAF50; margin: 0 0 10px 0; font-size: 20px; font-weight: 700;">
        SHIPMENT CREATED SUCCESSFULLY
      </h2>
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        ${currentDate}
      </p>
    </div>

    <div style="background-color: #f3fff3; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50; margin: 20px 0;">
      <h3 style="color: #4CAF50; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Hello ${name},
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Your shipment has been created successfully. We'll send you notifications when your shipment status is updated.
      </p>
    </div>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h4 style="color: #333; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
        SHIPMENT DETAILS
      </h4>
      
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
            Shipment ID:
          </td>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
            ${shipmentId}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: #555;">
            Tracking Code:
          </td>
          <td style="padding: 8px 0; border-bottom: 1px solid #e0e0e0; color: #333;">
            ${trackingCode}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: 600; color: #555;">
            Pickup Code:
          </td>
          <td style="padding: 8px 0;">
            <span style="background-color: #4CAF50; color: white; padding: 6px 15px; border-radius: 25px; font-size: 14px; font-weight: 700; letter-spacing: 2px;">
              ${pickupCode}
            </span>
          </td>
        </tr>
      </table>
    </div>

    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
        <strong>Important:</strong> Keep the pickup code safe. The recipient will need this code to collect the package.
      </p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 10px 0; color: #666; font-size: 14px;">
        Thank you for using Shipment Relay Platform
      </p>
      <p style="margin: 5px 0; color: #888; font-size: 12px;">
        Shipment Relay Team
      </p>
    </div>
  `;
}

/**
 * Generate modern shipment status update email
 * @param name Recipient's name
 * @param shipmentId Shipment ID
 * @param status Current status
 * @param trackingCode Tracking code
 * @param options Email template options
 * @param details Additional details
 * @returns Email template with subject and HTML
 */
export function generateModernShipmentUpdateEmail(
  name: string,
  shipmentId: string,
  status: string,
  trackingCode: string,
  options: { preferredLanguage: string; emailFormat: string },
  details?: any
): { subject: string; html: string } {
  const arabicSubject = `تحديث حالة الشحنة - ${shipmentId}`;
  const englishSubject = `Shipment Update - ${shipmentId}`;

  // Import the functions we need
  // eslint-disable-next-line no-undef
  const { modernBilingualTemplate } = require('./arabicEmailTemplates');
  // eslint-disable-next-line no-undef
  const { modernShipmentUpdateArabicContent } = require('./arabicEmailTemplates');

  switch (options.emailFormat) {
    case 'ARABIC_ONLY': {
      return {
        subject: arabicSubject,
        html: modernBilingualTemplate(
          modernShipmentUpdateArabicContent(name, shipmentId, status, trackingCode, details),
          ''
        )
      };
    }

    case 'ENGLISH_ONLY': {
      return {
        subject: englishSubject,
        html: modernBilingualTemplate(
          '',
          modernShipmentUpdateEnglishContent(name, shipmentId, status, trackingCode, details)
        )
      };
    }

    case 'BILINGUAL':
    default: {
      const arabicContent = modernShipmentUpdateArabicContent(name, shipmentId, status, trackingCode, details);
      const englishContent = modernShipmentUpdateEnglishContent(name, shipmentId, status, trackingCode, details);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate modern shipment created email
 * @param name Customer's name
 * @param shipmentId Shipment ID
 * @param pickupCode Pickup code
 * @param trackingCode Tracking code
 * @param options Email template options
 * @returns Email template with subject and HTML
 */
export function generateModernShipmentCreatedEmail(
  name: string,
  shipmentId: string,
  pickupCode: string,
  trackingCode: string,
  options: { preferredLanguage: string; emailFormat: string }
): { subject: string; html: string } {
  const arabicSubject = `تم إنشاء الشحنة - ${shipmentId}`;
  const englishSubject = `Shipment Created - ${shipmentId}`;

  // Import the functions we need
  // eslint-disable-next-line no-undef
  const { modernBilingualTemplate } = require('./arabicEmailTemplates');
  // eslint-disable-next-line no-undef
  const { modernShipmentCreatedArabicContent } = require('./arabicEmailTemplates');

  switch (options.emailFormat) {
    case 'ARABIC_ONLY': {
      return {
        subject: arabicSubject,
        html: modernBilingualTemplate(
          modernShipmentCreatedArabicContent(name, shipmentId, pickupCode, trackingCode),
          ''
        )
      };
    }

    case 'ENGLISH_ONLY': {
      return {
        subject: englishSubject,
        html: modernBilingualTemplate(
          '',
          modernShipmentCreatedEnglishContent(name, shipmentId, pickupCode, trackingCode)
        )
      };
    }

    case 'BILINGUAL':
    default: {
      const arabicContent = modernShipmentCreatedArabicContent(name, shipmentId, pickupCode, trackingCode);
      const englishContent = modernShipmentCreatedEnglishContent(name, shipmentId, pickupCode, trackingCode);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Modern welcome email content (English) - now combined in Arabic content function
 */
export function modernWelcomeEnglishContent(_name: string, _userType: string): string {
  // Content is now combined in the Arabic function
  return '';
}

/**
 * Modern verification OTP email content (English) - now combined in Arabic content function
 */
export function modernVerificationOTPEnglishContent(_name: string, _otp: string): string {
  // Content is now combined in the Arabic function
  return '';
}
