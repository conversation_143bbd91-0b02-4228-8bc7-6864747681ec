# Comprehensive Admin API Documentation

## Overview

This document provides a complete reference for the enhanced Admin API designed for the NAQALAT shipment relay platform. The API follows REST conventions and provides comprehensive administrative functionality.

**Base URL:** `/api/admin`

**Authentication:** All endpoints require admin authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standardized format:

```json
{
  "success": boolean,
  "message": string,
  "data": object | array,
  "pagination": {  // Only for paginated responses
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

## Common Query Parameters

- `page`: Page number for pagination (default: 0)
- `limit`: Items per page (default: 20, max: 100)
- `search`: Text search across relevant fields
- `sort`: Field to sort by (default: 'created_at')
- `order`: Sort order 'asc' or 'desc' (default: 'desc')

## 1. Authentication & Profile Management

### Login
**POST** `/login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Get Profile
**GET** `/profile`

### Update Profile
**PUT** `/profile`
```json
{
  "name": "Admin Name",
  "role": "ADMIN"
}
```

### Change Password
**POST** `/change-password`
```json
{
  "currentPassword": "oldpass",
  "newPassword": "newpass"
}
```

## 2. Dashboard & Analytics

### System Overview
**GET** `/analytics/overview`

Returns comprehensive system statistics including user counts, shipment metrics, and system health.

### User Analytics
**GET** `/analytics/users`

Returns user distribution by type, status, recent registrations, and pending approvals.

### Shipment Analytics
**GET** `/analytics/shipments`

Returns shipment distribution by status, recent activity, and performance metrics.

### Performance Metrics
**GET** `/analytics/performance`

Returns system performance data including response times, error rates, and resource usage.

### System Health
**GET** `/system/health`

Returns health status of all system components including database, memory, and services.

## 3. Enhanced User Management

### Get All Users
**GET** `/users`

**Query Parameters:**
- `search`: Search by name or email
- `user_type`: CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR
- `status`: ACTIVE, PENDING, SUSPENDED
- `approval_status`: APPROVED, PENDING, REJECTED

### Get User Details
**GET** `/users/:id`

Returns detailed user information including related data, recent shipments, and activity history.

### Update User
**PUT** `/users/:id`
```json
{
  "name": "Updated Name",
  "phone": "+**********",
  "status": "ACTIVE"
}
```

### Change User Status
**POST** `/users/:id/status`
```json
{
  "status": "SUSPENDED"
}
```

### Bulk Approve Operators
**POST** `/users/bulk-approve`
```json
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### Bulk Reject Operators
**POST** `/users/bulk-reject`
```json
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### Get User Activity
**GET** `/users/:id/activity`

Returns paginated user activity history (audit logs).

### Send Notification to User
**POST** `/users/:id/notify`
```json
{
  "title": "Important Notice",
  "message": "Your account has been updated.",
  "type": "SYSTEM",
  "priority": "HIGH"
}
```

## 4. Admin Management

### Get All Admins
**GET** `/admins`

### Create Admin
**POST** `/admins`
```json
{
  "name": "New Admin",
  "email": "<EMAIL>",
  "password": "securepassword",
  "role": "ADMIN"
}
```

### Get Admin Details
**GET** `/admins/:id`

### Update Admin
**PUT** `/admins/:id`
```json
{
  "name": "Updated Name",
  "role": "SUPPORT"
}
```

### Change Admin Status
**POST** `/admins/:id/status`
```json
{
  "status": "SUSPENDED"
}
```

## 5. Shipment Management

### Get All Shipments
**GET** `/shipments`

**Query Parameters:**
- `status`: Filter by shipment status
- `origin_ao_id`: Filter by origin access operator
- `dest_ao_id`: Filter by destination access operator
- `customer_id`: Filter by customer

### Get Shipment Details
**GET** `/shipments/:id`

### Update Shipment
**PUT** `/shipments/:id`
```json
{
  "status": "IN_TRANSIT",
  "notes": "Admin override"
}
```

### Update Shipment Status
**POST** `/shipments/:id/status`
```json
{
  "status": "CANCELLED",
  "reason": "Customer request"
}
```

### Bulk Cancel Shipments
**POST** `/shipments/bulk-cancel`
```json
{
  "shipment_ids": ["uuid1", "uuid2"],
  "reason": "System maintenance"
}
```

### Get Expired Shipments
**GET** `/shipments/expired`

### Reassign Shipment
**POST** `/shipments/:id/reassign`
```json
{
  "new_origin_ao_id": "uuid",
  "new_dest_ao_id": "uuid",
  "reason": "Operator unavailable"
}
```

## Error Handling

The API uses standard HTTP status codes:

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Error responses include detailed messages:

```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "type": "ERROR_TYPE",
    "details": "Detailed error information"
  }
}
```

## Rate Limiting

- Standard endpoints: 100 requests per minute
- Bulk operations: 10 requests per minute
- Authentication endpoints: 5 requests per minute

## 6. System Settings Management

### Get System Settings
**GET** `/settings`

Returns all system configuration settings.

### Update System Settings
**PUT** `/settings`
```json
{
  "min_distance_km": 2.0,
  "max_shipments_per_day": 10,
  "require_photo_proof": true,
  "enable_2fa": true
}
```

### Reset System Settings
**POST** `/settings/reset`

Resets all settings to default values.

### Get Settings History
**GET** `/settings/history`

Returns history of settings changes with timestamps and admin details.

## 7. Notification Management

### Get All Notifications
**GET** `/notifications`

**Query Parameters:**
- `user_id`: Filter by user
- `type`: Filter by notification type
- `status`: read, unread
- `priority`: LOW, NORMAL, HIGH, URGENT

### Broadcast Notification
**POST** `/notifications/broadcast`
```json
{
  "title": "System Maintenance",
  "message": "Scheduled maintenance tonight",
  "user_types": ["CUSTOMER", "ACCESS_OPERATOR"],
  "priority": "HIGH"
}
```

### Get Notification Templates
**GET** `/notifications/templates`

### Create Notification Template
**POST** `/notifications/templates`
```json
{
  "name": "Welcome Template",
  "title": "Welcome to NAQALAT",
  "message": "Thank you for joining our platform",
  "type": "WELCOME"
}
```

### Update Notification Template
**PUT** `/notifications/templates/:id`

### Get Notification Statistics
**GET** `/notifications/stats`

Returns notification delivery statistics and engagement metrics.

## 8. Audit & Security

### Get Audit Logs
**GET** `/audit-logs`

**Query Parameters:**
- `user_id`: Filter by user
- `admin_id`: Filter by admin
- `action`: Filter by action type
- `date_from`: Start date (ISO format)
- `date_to`: End date (ISO format)

### Get Login Attempts
**GET** `/security/login-attempts`

**Query Parameters:**
- `successful`: true/false
- `ip_address`: Filter by IP
- `date_from`: Start date

### Get Security Events
**GET** `/security/events`

Returns security-related events and alerts.

### Lock User Account
**POST** `/security/lock-user`
```json
{
  "user_id": "uuid",
  "reason": "Suspicious activity detected"
}
```

### Unlock User Account
**POST** `/security/unlock-user`
```json
{
  "user_id": "uuid",
  "reason": "Issue resolved"
}
```

## 9. Reporting & Export

### User Reports
**GET** `/reports/users`

**Query Parameters:**
- `format`: json, csv, excel
- `date_from`: Start date
- `date_to`: End date
- `user_type`: Filter by user type

### Shipment Reports
**GET** `/reports/shipments`

**Query Parameters:**
- `format`: json, csv, excel
- `status`: Filter by status
- `date_from`: Start date
- `date_to`: End date

### System Reports
**GET** `/reports/system`

Returns comprehensive system performance and usage reports.

### Export Users
**POST** `/export/users`
```json
{
  "format": "csv",
  "filters": {
    "user_type": "CUSTOMER",
    "status": "ACTIVE"
  }
}
```

Returns export job ID for tracking.

### Export Shipments
**POST** `/export/shipments`
```json
{
  "format": "excel",
  "filters": {
    "status": "DELIVERED",
    "date_from": "2024-01-01"
  }
}
```

### Download Export
**GET** `/export/:id/download`

Downloads the completed export file.

## Security Features

- JWT-based authentication
- Role-based access control (ADMIN, AUDITOR, SUPPORT)
- Request validation and sanitization
- Audit logging for all admin actions
- Rate limiting and abuse protection
- IP-based access controls
- Session management and timeout

## Implementation Notes for Frontend

### Authentication Flow
1. Login with email/password
2. Store JWT token securely
3. Include token in all subsequent requests
4. Handle token expiration and refresh

### Error Handling
- Implement global error handler
- Show user-friendly error messages
- Log errors for debugging
- Handle network failures gracefully

### Performance Optimization
- Implement pagination for large datasets
- Use debouncing for search inputs
- Cache frequently accessed data
- Implement lazy loading for heavy components

### Real-time Features
- Consider WebSocket connection for real-time updates
- Implement notification system
- Show live system status
- Real-time dashboard metrics

### Accessibility
- Follow WCAG guidelines
- Implement keyboard navigation
- Provide screen reader support
- Use semantic HTML elements

### Mobile Responsiveness
- Design mobile-first approach
- Optimize for touch interfaces
- Consider offline functionality
- Implement progressive web app features
